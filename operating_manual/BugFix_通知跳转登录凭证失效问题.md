# Bug修复：通知跳转导致登录凭证失效问题

## 问题描述

当用户在其他模块（如资料核对模块）工作时，如果有加急片弹窗显示，点击通知栏通知跳转到加急页面后，点击返回上一页就会显示"登录凭证失效"，自动跳转到登录页面。

## 问题原因分析

### 根本原因
通知点击时使用了 `Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK` 标志组合，这会：

1. **清除整个任务栈**：`FLAG_ACTIVITY_CLEAR_TASK` 会清除当前应用的所有Activity
2. **创建新任务**：`FLAG_ACTIVITY_NEW_TASK` 会在新的任务中启动Activity
3. **破坏Activity栈**：用户原本在资料核对页面，但任务栈被清除后，返回时没有可返回的Activity

### 触发流程
1. 用户在资料核对模块工作
2. 加急系统检测到新数据，发送通知
3. 用户点击通知，系统使用 `FLAG_ACTIVITY_CLEAR_TASK` 清除所有Activity
4. 跳转到加急页面
5. 用户点击返回，发现没有可返回的Activity
6. 应用重新启动，触发登录检查，跳转到登录页面

## 修复方案

### 1. 修改Intent标志
将所有通知相关的Intent标志从：
```kotlin
flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
```

修改为：
```kotlin
flags = Intent.FLAG_ACTIVITY_SINGLE_TOP or Intent.FLAG_ACTIVITY_CLEAR_TOP
```

### 2. 修改PendingIntent标志
添加 `FLAG_UPDATE_CURRENT` 标志确保Intent数据能正确更新：
```kotlin
PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
```

### 3. 配置Activity启动模式
在AndroidManifest.xml中为UrgentSystemActivity添加：
```xml
android:launchMode="singleTop"
```

### 4. 添加onNewIntent处理
在UrgentSystemActivity中添加onNewIntent方法处理通知点击：
```kotlin
override fun onNewIntent(intent: Intent?) {
    super.onNewIntent(intent)
    intent?.getStringExtra("site")?.let { site ->
        if (site != "请选择站点" && site != tvSite.text.toString()) {
            tvSite.text = site
            updateSite(site)
        }
    }
}
```

## 修改的文件

### 1. UrgentSystemActivity.kt
- 修改 `createInitialNotification()` 方法中的Intent标志
- 修改 `updateNotification()` 方法中的Intent标志  
- 添加 `onNewIntent()` 方法处理通知点击

### 2. UrgentTimerService.kt
- 修改 `showUrgentNotification()` 方法中的Intent标志

### 3. UrgentNotificationService.kt
- 修改 `createNotification()` 方法中的Intent标志
- 简化PendingIntent创建逻辑，移除TaskStackBuilder

### 4. AndroidManifest.xml
- 为UrgentSystemActivity添加 `android:launchMode="singleTop"`

## 修复效果

### 修复前
- 点击通知 → 清除所有Activity → 跳转加急页面 → 返回 → 无可返回Activity → 重启应用 → 登录页面

### 修复后  
- 点击通知 → 保持Activity栈 → 跳转/复用加急页面 → 返回 → 回到原来的页面 ✅

## 技术说明

### Intent标志说明
- `FLAG_ACTIVITY_SINGLE_TOP`：如果目标Activity已在栈顶，则复用而不创建新实例
- `FLAG_ACTIVITY_CLEAR_TOP`：清除目标Activity之上的所有Activity，但保留栈底Activity
- `FLAG_UPDATE_CURRENT`：更新已存在的PendingIntent的额外数据

### 启动模式说明
- `singleTop`：当Activity已在栈顶时，不会创建新实例，而是调用onNewIntent()

## 测试建议

1. **基本功能测试**：
   - 在资料核对页面工作
   - 触发加急通知
   - 点击通知跳转
   - 点击返回按钮
   - 验证是否回到资料核对页面

2. **多场景测试**：
   - 从不同模块（盘点、芯片CheckIn/Out）测试通知跳转
   - 测试多次通知点击的行为
   - 测试应用在后台时的通知处理

3. **边界情况测试**：
   - 应用被系统杀死后的通知点击
   - 快速连续点击通知的处理
   - 不同站点通知的切换

## 注意事项

1. 此修复保持了Activity栈的完整性，用户体验更加流畅
2. 通知功能依然正常工作，只是改变了Activity的启动方式
3. 修复后需要全面测试各种通知场景，确保没有引入新问题
4. 建议在生产环境部署前进行充分的回归测试
