plugins {
    id 'com.android.application'
    id 'kotlin-android'
    id 'kotlin-android-extensions'
    id 'kotlin-kapt'
}

android {
    compileSdkVersion 29
    buildToolsVersion "29.0.2"

    defaultConfig {
        applicationId "com.example.ah_geen_pda"
        minSdkVersion 21
        //noinspection ExpiredTargetSdkVersion
        targetSdkVersion 29
        versionCode 1
        versionName "1.2.5"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    dataBinding{
        enabled true
    }

    repositories {
        flatDir {
            dirs 'libs'
        }
    }
    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
        }
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation 'androidx.appcompat:appcompat:1.1.0'
    implementation 'androidx.constraintlayout:constraintlayout:1.1.3'
    implementation 'com.github.CymChad:BaseRecyclerViewAdapterHelper:2.9.30'
    implementation 'com.google.code.gson:gson:2.8.1'
    implementation "com.jakewharton:butterknife:10.0.0"
    implementation 'com.google.android.material:material:1.3.0'
    testImplementation 'junit:junit:4.12'
    testImplementation 'junit:junit:4.12'
    kapt "com.jakewharton:butterknife-compiler:10.0.0"
    implementation 'com.lzy.net:okgo:3.0.4'
    implementation 'com.lzy.net:okserver:2.0.5'
    implementation 'cn.bingoogolapple:bga-qrcode-zbar:1.3.4'
    implementation 'cjt.library.wheel:camera:0.1.9'
    implementation 'pub.devrel:easypermissions:1.0.1'
    implementation 'com.github.RmondJone:LockTableView:1.1.2'
    implementation(name: 'app-debug', ext: 'aar')
    implementation group: 'org.apache.commons', name: 'commons-lang3', version: '3.3.2'
    implementation group: 'com.alibaba', name: 'fastjson', version: '1.2.58'
    implementation 'com.chensl.rotatephotoview:rotatephotoview:1.0.5'
    implementation 'me.tatarka.bindingcollectionadapter2:bindingcollectionadapter-recyclerview:4.0.0'
    implementation 'me.tatarka.bindingcollectionadapter2:bindingcollectionadapter:4.0.0'
    implementation 'com.ljx.rxhttp:rxhttp:2.5.7'
    implementation 'com.squareup.okhttp3:okhttp:4.9.0' //rxhttp v2.2.2版本起，需要手动依赖okhttp
    kapt 'com.ljx.rxhttp:rxhttp-compiler:2.5.7'
    implementation 'com.ljx.rxlife:rxlife-coroutine:2.0.1' //管理协程生命周期，页面销毁，关闭请求
    implementation 'io.reactivex.rxjava3:rxjava:3.0.6'
    implementation 'io.reactivex.rxjava3:rxandroid:3.0.0'
    implementation 'com.ljx.rxlife3:rxlife-rxjava:3.0.0' //管理RxJava3生命周期，页面销毁，关闭请求
    implementation 'com.afollestad.material-dialogs:lifecycle:3.1.1'
    implementation 'com.afollestad.material-dialogs:core:3.1.1'
    implementation 'com.gyf.immersionbar:immersionbar:2.3.3'
    implementation 'androidx.lifecycle:lifecycle-extensions:2.2.0'
    implementation "androidx.core:core-ktx:1.6.0"
    implementation 'com.google.zxing:core:3.0.1'
    implementation 'com.journeyapps:zxing-android-embedded:2.0.1@aar'
    implementation 'com.journeyapps:zxing-android-legacy:2.0.1@aar'
    implementation 'com.journeyapps:zxing-android-integration:2.0.1@aar'
    implementation "androidx.lifecycle:lifecycle-process:2.2.0"
    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:2.2.0"
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
}
repositories {
    mavenCentral()
}