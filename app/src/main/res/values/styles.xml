<resources>
    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style>

    <style name="text_title">
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">@dimen/free_ui_sp_16</item>
        <item name="android:paddingLeft">@dimen/free_ui_sp_12</item>
        <item name="android:paddingRight">@dimen/free_ui_sp_12</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:background">@color/free_ui_white</item>
        <item name="android:textColor">@color/free_ui_glory_theme_color</item>
    </style>

    <style name="text_edit_linearLayout">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/free_ui_dp_48</item>
        <item name="android:layout_marginTop">@dimen/free_ui_dp_1</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:paddingLeft">@dimen/free_ui_dp_12</item>
        <item name="android:paddingRight">@dimen/free_ui_dp_12</item>
    </style>

    <style name="clear_edit">
        <item name="android:layout_width">@dimen/free_ui_dp_0</item>
        <item name="android:layout_weight">7</item>
        <item name="android:layout_height">@dimen/free_ui_dp_48</item>
        <item name="background">@drawable/free_ui_glory_selector_edittext</item>
        <item name="android:textColor">@color/free_ui_dark_gold_text_black</item>
        <item name="android:textColorHint">@color/free_ui_dark_gold_text_gray</item>
        <item name="singleLine">true</item>
        <item name="android:textSize">@dimen/free_ui_sp_14</item>
    </style>

</resources>