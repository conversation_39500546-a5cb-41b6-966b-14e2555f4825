<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        tools:context=".MainActivity">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/free_ui_glory_theme_color"
            android:minHeight="?attr/actionBarSize">

            <TextView
                android:text="首页"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_gravity="center"
                android:textColor="@color/free_ui_white"
                android:textSize="20sp" />

        </androidx.appcompat.widget.Toolbar>

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/free_ui_glory_bg">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/llMFGLayout"
                    android:orientation="vertical"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rvMFGList"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/free_ui_dp_8"
                        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                        app:spanCount="2"
                        android:background="@color/free_ui_white" />

                </LinearLayout>

            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </LinearLayout>
</layout>