<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="UrgentSystemViewModel"
            type="com.example.ah_geen_pda.ui.urgentsystem.UrgentSystemViewModel" />
    </data>

    <LinearLayout
        android:id="@+id/llMain"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/free_ui_glory_bg"
        android:orientation="vertical">

        <include layout="@layout/activity_title" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/free_ui_dp_48"
                    android:layout_marginTop="@dimen/free_ui_dp_1"
                    android:background="@color/free_ui_white"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/free_ui_dp_12"
                    android:paddingRight="@dimen/free_ui_dp_12">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="0.3"
                        android:text="站点:"
                        android:textColor="@color/free_ui_glory_theme_color"
                        android:textSize="@dimen/sp_14" />


                    <FrameLayout
                        android:id="@+id/flSiteList"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/free_ui_dp_36"
                        android:layout_marginLeft="@dimen/dp_4"
                        android:layout_weight="1"
                        android:background="@drawable/free_ui_glory_selector_kuang_button_click_background"
                        android:paddingLeft="@dimen/free_ui_dp_8"
                        android:paddingRight="@dimen/free_ui_dp_8">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <TextView
                                android:id="@+id/tvSite"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:gravity="center_vertical"
                                android:singleLine="true"
                                android:textColor="@color/free_ui_dark_gold_text_black"
                                android:textColorHint="@color/free_ui_dark_gold_text_gray"
                                android:textSize="@dimen/sp_14" />
                        </LinearLayout>

                        <ImageView
                            android:id="@+id/ivSiteArrow"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="right|center_vertical"
                            android:src="@drawable/free_ui_glory_down_arrow" />
                    </FrameLayout>


                    <Button
                        android:id="@+id/btCheckData"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/dp_40"
                        android:layout_marginLeft="@dimen/dp_10"
                        android:layout_weight="0.5"
                        android:background="@drawable/free_ui_glory_selector_button_click_background"
                        android:text="查询资料"
                        android:textColor="@color/free_ui_white"
                        android:textSize="@dimen/sp_14" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/free_ui_dp_8"
                    android:background="@color/free_ui_glory_theme_color"
                    android:minHeight="@dimen/free_ui_dp_48"
                    android:orientation="horizontal">


                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="载具号"
                        android:textColor="@color/white" />


                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="片数"
                        android:textColor="@color/white" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="状态"
                        android:textColor="@color/white" />


                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="扣留"
                        android:textColor="@color/white" />


                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="站点"
                        android:textColor="@color/white" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="停留时间"
                        android:textColor="@color/white" />


                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    items="@{UrgentSystemViewModel.dataItems}"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:itemBinding="@{UrgentSystemViewModel.durableDataBinding}"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />


            </LinearLayout>

        </androidx.core.widget.NestedScrollView>
    </LinearLayout>
</layout>