<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="dataItem"
            type="com.example.ah_geen_pda.entity.UrgentFilmInformation" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:gravity="center_vertical"
        android:minHeight="@dimen/free_ui_dp_48"
        android:layout_marginTop="@dimen/free_ui_dp_1"
        android:orientation="horizontal"

        app:detainColor="@{dataItem.Detain}">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@{dataItem.BoxNumber}"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_14" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@{dataItem.MinQty}"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_14" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@{dataItem.State}"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_14" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@{dataItem.Detain}"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_14" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@{dataItem.Site}"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_14" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@{dataItem.StayTime}"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_14" />

    </LinearLayout>
</layout>