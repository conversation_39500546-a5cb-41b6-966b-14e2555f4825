<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>
        <variable
            name="tabIndexGrid"
            type="com.pda.platform.ui.ui_pdaplatform.entity.FreeUI_TabIndexGridEntity" />
    </data>

    <LinearLayout
        android:id="@+id/llTab"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/free_ui_selector_white_gray_click"
        android:gravity="center_vertical"
        android:paddingTop="@dimen/free_ui_dp_20"
        android:paddingBottom="@dimen/free_ui_dp_20">

        <TextView
            android:id="@+id/tvTab"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:drawablePadding="@dimen/free_ui_dp_16"
            android:gravity="center"
            android:text="@{tabIndexGrid.text}"
            compoundDrawableTop="@{tabIndexGrid.drawable}"
            android:textColor="@color/free_ui_glory_theme_color" />
    </LinearLayout>
</layout>
