<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="@dimen/free_ui_dp_20"
    android:background="@color/free_ui_white"
    android:minWidth="320dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="配件信息输入"
        android:textSize="@dimen/free_ui_sp_16"
        android:textColor="@color/free_ui_glory_theme_color"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="@dimen/free_ui_dp_16" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="@dimen/free_ui_dp_12">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1.5"
            android:text="配件1:"
            android:textColor="@color/free_ui_glory_theme_color"
            android:textSize="@dimen/free_ui_sp_14" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="*"
            android:textColor="@android:color/holo_red_dark"
            android:textSize="@dimen/free_ui_sp_14"
            android:layout_marginRight="@dimen/free_ui_dp_8" />

        <com.pda.platform.ui.ui_pdaplatform.view.FreeUI_ClearEditText
                android:id="@+id/etMaterial1"
                android:layout_width="0dp"
                android:layout_height="@dimen/free_ui_dp_44"
                android:layout_weight="6"
                android:background="@drawable/free_ui_glory_selector_edittext"
                android:hint="请输入配件1"
                android:paddingLeft="@dimen/free_ui_dp_8"
                android:paddingRight="@dimen/free_ui_dp_8"
                android:singleLine="true"
                android:imeOptions="actionDone"
                android:inputType="text"
                android:textColor="@color/free_ui_dark_gold_text_black"
                android:textColorHint="@color/free_ui_dark_gold_text_gray"
                android:textSize="@dimen/free_ui_sp_14"
                tools:ignore="TouchTargetSizeCheck" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="@dimen/free_ui_dp_20">

        <TextView
                android:layout_width="6dp"
                android:layout_height="wrap_content"
                android:layout_weight="1.5"
                android:text="配件2:"
                android:textColor="@color/free_ui_glory_theme_color"
                android:textSize="@dimen/free_ui_sp_14"
                android:width="@dimen/free_ui_dp_64" />


        <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text=""
                android:layout_marginRight="@dimen/free_ui_dp_8" />

        <com.pda.platform.ui.ui_pdaplatform.view.FreeUI_ClearEditText
                android:id="@+id/etMaterial2"
                android:layout_width="0dp"
                android:layout_height="@dimen/free_ui_dp_44"
                android:layout_weight="6"
                android:background="@drawable/free_ui_glory_selector_edittext"
                android:hint="请输入配件2(可选)"
                android:paddingLeft="@dimen/free_ui_dp_8"
                android:paddingRight="@dimen/free_ui_dp_8"
                android:singleLine="true"
                android:imeOptions="actionDone"
                android:inputType="text"
                android:textColor="@color/free_ui_dark_gold_text_black"
                android:textColorHint="@color/free_ui_dark_gold_text_gray"
                android:textSize="@dimen/free_ui_sp_14"
                tools:ignore="TouchTargetSizeCheck" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center">

        <Button
                android:id="@+id/btnCancel"
                android:layout_width="0dp"
                android:layout_height="@dimen/free_ui_dp_44"
                android:layout_weight="1"
                android:layout_marginRight="@dimen/free_ui_dp_8"
                android:background="@drawable/cancel_button"
                android:text="取消"
                android:textColor="@color/free_ui_dark_gold_text_black"
                android:textSize="@dimen/free_ui_sp_14"
                tools:ignore="TouchTargetSizeCheck" />

        <Button
                android:id="@+id/btnConfirm"
                android:layout_width="0dp"
                android:layout_height="@dimen/free_ui_dp_44"
                android:layout_weight="1"
                android:layout_marginLeft="@dimen/free_ui_dp_8"
                android:background="@drawable/free_ui_glory_selector_button_click_background"
                android:text="确定"
                android:textColor="@color/free_ui_white"
                android:textSize="@dimen/free_ui_sp_14"
                tools:ignore="TouchTargetSizeCheck" />
    </LinearLayout>

</LinearLayout>
