<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="loginViewModel"
            type="com.example.ah_geen_pda.login.LoginViewModel" />
    </data>

    <RelativeLayout
        android:id="@+id/rlParent"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".ui.login.view.LoginActivity"
        android:background="@color/white">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/ivSetting"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/free_ui_dp_8"
                android:layout_margin="@dimen/free_ui_dp_12"
                android:src="@drawable/free_ui_glory_selector_setup_click" />

        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/imageView2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:srcCompat="@drawable/log" />

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:src="@mipmap/ic_launcher"
                android:visibility="gone" />

            <LinearLayout
                android:layout_width="240dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_40"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="账号："
                    android:textColor="@color/free_ui_glory_theme_color" />

                <com.pda.platform.ui.ui_pdaplatform.view.FreeUI_ClearEditText
                    android:id="@+id/etUser"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/free_ui_dp_44"
                    android:layout_weight="1"
                    android:background="@drawable/free_ui_glory_selector_edittext"
                    android:hint="请输入用户名"
                    android:paddingLeft="@dimen/free_ui_dp_8"
                    android:paddingRight="@dimen/free_ui_dp_8"
                    android:singleLine="true"
                    android:textColor="@color/free_ui_dark_gold_text_black"
                    android:textColorHint="@color/free_ui_dark_gold_text_gray"
                    android:textSize="@dimen/sp_14" />
            </LinearLayout>


            <LinearLayout
                android:layout_width="240dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/free_ui_dp_12"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="密码："
                    android:textColor="@color/free_ui_glory_theme_color" />

                <com.pda.platform.ui.ui_pdaplatform.view.FreeUI_PasswordEditText
                    android:id="@+id/etPassword"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/free_ui_dp_44"
                    android:layout_weight="1"
                    android:background="@drawable/free_ui_glory_selector_edittext"
                    android:hint="请输入密码"
                    android:inputType="textPassword"
                    android:paddingLeft="@dimen/free_ui_dp_8"
                    android:paddingRight="@dimen/free_ui_dp_8"
                    android:singleLine="true"
                    android:textColor="@color/free_ui_dark_gold_text_black"
                    android:textColorHint="@color/free_ui_dark_gold_text_gray"
                    android:textSize="@dimen/sp_14" />
            </LinearLayout>


            <Button
                android:id="@+id/btnLogin"
                android:layout_width="240dp"
                android:layout_height="@dimen/dp_40"
                android:layout_marginTop="40dp"
                android:background="@drawable/free_ui_glory_selector_button_click_background"
                android:text="登录"
                android:textColor="@color/free_ui_white"
                android:textSize="@dimen/sp_14" />

        </LinearLayout>
    </RelativeLayout>
</layout>
