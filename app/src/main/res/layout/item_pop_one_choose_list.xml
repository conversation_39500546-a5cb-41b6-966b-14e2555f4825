<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/selector_gray_bg_click"
    android:gravity="center_vertical"
    android:minHeight="@dimen/dp_40"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tvContent"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:paddingLeft="@dimen/free_ui_dp_12"
        android:paddingRight="@dimen/free_ui_dp_12"
        android:textColor="@color/text_middle_black"
        android:textSize="@dimen/sp_14" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/free_ui_dp_1"
        android:background="@color/select_gray" />
</LinearLayout>
