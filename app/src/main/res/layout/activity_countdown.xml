<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
            name="countdownViewModel"
            type="com.example.ah_geen_pda.ui.countdown.CountdownViewModel" />
    </data>

    <LinearLayout
        android:id="@+id/llMain"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/free_ui_glory_bg"
        android:orientation="vertical">

        <!-- 标题栏 -->
        <include layout="@layout/activity_title" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- 倒计时显示区域 - 美化版 -->
                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/free_ui_dp_16"
                    android:layout_marginLeft="@dimen/free_ui_dp_12"
                    android:layout_marginRight="@dimen/free_ui_dp_12"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="8dp"
                    app:cardBackgroundColor="@color/free_ui_white">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="@dimen/free_ui_dp_24">

                        <!-- 倒计时标题 -->
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="@dimen/free_ui_dp_8"
                            android:text="倒计时器"
                            android:textColor="@color/free_ui_glory_theme_color"
                            android:textSize="@dimen/sp_20"
                            android:textStyle="bold" />

                        <!-- 自定义环形进度条 - 增大尺寸 -->
                        <com.example.ah_geen_pda.ui.countdown.CircularProgressView
                                android:id="@+id/circularProgressView"
                                android:layout_width="280dp"
                                android:layout_height="280dp"
                                android:layout_gravity="center"
                                android:layout_marginTop="@dimen/free_ui_dp_8"
                                android:layout_marginBottom="@dimen/free_ui_dp_16" />

                        <!-- 倒计时状态文本 - 美化样式 -->
                        <TextView
                            android:id="@+id/tvCountdownStatus"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/free_ui_dp_8"
                            android:text="准备开始"
                            android:textColor="@color/free_ui_glory_theme_color"
                            android:textSize="@dimen/sp_18"
                            android:textStyle="bold"
                            android:background="@drawable/rounded_background"
                            android:paddingLeft="@dimen/free_ui_dp_16"
                            android:paddingRight="@dimen/free_ui_dp_16"
                            android:paddingTop="@dimen/free_ui_dp_8"
                            android:paddingBottom="@dimen/free_ui_dp_8" />

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

                <!-- 载具输入区域 - 美化版 -->
                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/free_ui_dp_8"
                    android:layout_marginLeft="@dimen/free_ui_dp_12"
                    android:layout_marginRight="@dimen/free_ui_dp_12"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    app:cardBackgroundColor="@color/free_ui_white">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/free_ui_dp_56"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingLeft="@dimen/free_ui_dp_16"
                        android:paddingRight="@dimen/free_ui_dp_16">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="3"
                            android:text="载具号:"
                            android:textColor="@color/free_ui_glory_theme_color"
                            android:textSize="@dimen/sp_16"
                            android:textStyle="bold" />

                        <EditText
                            android:id="@+id/etDurable"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="7"
                            android:background="@drawable/edit_text_background"
                            android:gravity="center_vertical"
                            android:hint="请输入载具号"
                            android:imeOptions="actionGo"
                            android:inputType="text"
                            android:maxLines="1"
                            android:paddingLeft="@dimen/free_ui_dp_12"
                            android:paddingRight="@dimen/free_ui_dp_12"
                            android:textColor="@color/free_ui_black"
                            android:textColorHint="@color/free_ui_gray"
                            android:textSize="@dimen/sp_16" />

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

                <!-- 开始按钮 - 美化版 -->
                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/free_ui_dp_8"
                    android:layout_marginLeft="@dimen/free_ui_dp_12"
                    android:layout_marginRight="@dimen/free_ui_dp_12"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    app:cardBackgroundColor="@color/free_ui_white">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="@dimen/free_ui_dp_16">

                        <Button
                            android:id="@+id/btnStart"
                            android:layout_width="0dp"
                            android:layout_height="@dimen/free_ui_dp_56"
                            android:layout_weight="1"
                            android:background="@drawable/gradient_button_background"
                            android:text="开始倒计时"
                            android:textColor="@color/free_ui_white"
                            android:textSize="@dimen/sp_18"
                            android:textStyle="bold"
                            android:elevation="4dp" />

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

                <!-- 批次列表区域 - 美化版 -->
                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/free_ui_dp_8"
                    android:layout_marginLeft="@dimen/free_ui_dp_12"
                    android:layout_marginRight="@dimen/free_ui_dp_12"
                    android:layout_marginBottom="@dimen/free_ui_dp_16"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    app:cardBackgroundColor="@color/free_ui_white">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <!-- 批次列表标题 -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/free_ui_dp_56"
                            android:gravity="center_vertical"
                            android:orientation="horizontal"
                            android:paddingLeft="@dimen/free_ui_dp_16"
                            android:paddingRight="@dimen/free_ui_dp_16"
                            android:background="@drawable/list_header_background">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="批次列表"
                                android:textColor="@color/free_ui_glory_theme_color"
                                android:textSize="@dimen/sp_18"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/tvLotCount"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="@dimen/free_ui_dp_8"
                                android:text="(0)"
                                android:textColor="@color/free_ui_gray"
                                android:textSize="@dimen/sp_16"
                                android:textStyle="bold" />

                        </LinearLayout>

                        <!-- 分割线 -->
                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:background="@color/free_ui_line_color" />

                        <!-- 批次列表 -->
                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rvLotList"
                            items="@{countdownViewModel.lotItems}"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:minHeight="200dp"
                            android:nestedScrollingEnabled="false"
                            android:padding="@dimen/free_ui_dp_8"
                            app:itemBinding="@{countdownViewModel.lotItemBinding}"
                            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

    </LinearLayout>
</layout>
