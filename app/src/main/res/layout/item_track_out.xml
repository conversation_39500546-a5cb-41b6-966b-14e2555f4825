<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>

        <variable
            name="trackOutItem"
            type="com.example.ah_geen_pda.entity.LotEntity" />
        <variable
            name="onDeleteClick"
            type="com.example.ah_geen_pda.ui.chipfrontsectionout.ChipFrontSectionOutViewModel.DeleteLot" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:gravity="center_vertical"
        android:minHeight="@dimen/free_ui_dp_48"
        android:layout_marginTop="@dimen/free_ui_dp_1"
        android:orientation="horizontal">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@{trackOutItem.LOT.DURABLEID}"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_14" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@{trackOutItem.LOT.LOTID}"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_14" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@{trackOutItem.LOT.MAINQTY}"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_14" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@{trackOutItem.LOT.PARTNAME}"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_14" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@{trackOutItem.LOT.STEPDESC}"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_14" />

        <TextView
            android:onClick="@{()-> onDeleteClick.onDelete(trackOutItem)}"
            android:text="删除"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:textColor="@color/red"
            android:textSize="@dimen/sp_14" />

    </LinearLayout>
</layout>