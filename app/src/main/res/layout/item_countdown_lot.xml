<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>
        <variable
            name="lotItem"
            type="com.example.ah_geen_pda.entity.LotEntity" />
        <variable
            name="onDeleteClick"
            type="com.example.ah_geen_pda.ui.countdown.CountdownViewModel.DeleteLot" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/free_ui_white"
        android:orientation="vertical"
        android:padding="@dimen/free_ui_dp_12">

        <!-- 批次信息主体 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <!-- 左侧信息区域 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <!-- 批次ID -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="批次ID: "
                        android:textColor="@color/free_ui_glory_theme_color"
                        android:textSize="@dimen/sp_14"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@{lotItem.LOT.LOTID}"
                        android:textColor="@color/free_ui_black"
                        android:textSize="@dimen/sp_14" />

                </LinearLayout>

                <!-- 水平父容器 -->
                <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginTop="@dimen/free_ui_dp_4">

                    <!-- 载具ID组 -->
                    <LinearLayout
                            android:layout_width="100dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="horizontal">

                        <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="载具ID: "
                                android:textColor="@color/free_ui_glory_theme_color"
                                android:textSize="@dimen/sp_12" />

                        <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{lotItem.LOT.DURABLEID}"
                                android:textColor="@color/free_ui_gray"
                                android:textSize="@dimen/sp_12" />
                    </LinearLayout>

                    <!-- 数量组 -->
                    <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="horizontal">

                        <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="数量: "
                                android:textColor="@color/free_ui_glory_theme_color"
                                android:textSize="@dimen/sp_12" />

                        <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{lotItem.LOT.MAINQTY}"
                                android:textColor="@color/free_ui_gray"
                                android:textSize="@dimen/sp_12" />
                    </LinearLayout>
                </LinearLayout>


                <!-- 水平父容器 -->
                <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginTop="@dimen/free_ui_dp_4">


                    <!-- 程序 -->
                    <LinearLayout
                            android:layout_width="100dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="horizontal">

                        <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="程序: "
                                android:textColor="@color/free_ui_glory_theme_color"
                                android:textSize="@dimen/sp_12" />

                        <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{lotItem.RECIPEDESC}"
                                android:textColor="@color/free_ui_gray"
                                android:textSize="@dimen/sp_12" />

                    </LinearLayout>

                    <!-- 状态 -->
                    <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="状态: "
                            android:textColor="@color/free_ui_glory_theme_color"
                            android:textSize="@dimen/sp_12" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@{lotItem.LOT.STATE}"
                            android:textColor="@color/free_ui_gray"
                            android:textSize="@dimen/sp_12" />

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

            <!-- 右侧删除按钮 -->
            <ImageView
                android:id="@+id/ivDelete"
                android:layout_width="@dimen/free_ui_dp_32"
                android:layout_height="@dimen/free_ui_dp_32"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="@dimen/free_ui_dp_8"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:onClick="@{() -> onDeleteClick.onDelete(lotItem)}"
                android:padding="@dimen/free_ui_dp_4"
                android:src="@drawable/ic_delete"
                android:contentDescription="删除批次" />

        </LinearLayout>

        <!-- 分割线 -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="@dimen/free_ui_dp_8"
            android:background="@color/free_ui_line_color" />

    </LinearLayout>

</layout>
