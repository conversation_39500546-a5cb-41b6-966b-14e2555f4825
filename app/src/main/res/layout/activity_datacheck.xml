<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="dataCheckViewModel"
            type="com.example.ah_geen_pda.ui.datacheck.DataCheckViewModel" />
    </data>

    <LinearLayout
        android:id="@+id/llMain"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/free_ui_glory_bg"
        android:orientation="vertical">

        <include layout="@layout/activity_title" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/free_ui_dp_48"
                    android:layout_marginTop="@dimen/free_ui_dp_1"
                    android:background="@color/free_ui_white"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/free_ui_dp_12"
                    android:paddingRight="@dimen/free_ui_dp_12">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="3"
                        android:text="载具号:"
                        android:textColor="@color/free_ui_glory_theme_color"
                        android:textSize="@dimen/sp_14" />

                    <com.pda.platform.ui.ui_pdaplatform.view.FreeUI_ClearEditText
                        android:id="@+id/etDurable"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/free_ui_dp_48"
                        android:layout_weight="7"
                        android:background="@drawable/free_ui_glory_selector_edittext"
                        android:focusable="true"
                        android:hint="请扫描载具号"
                        android:textColor="@color/free_ui_dark_gold_text_black"
                        android:textColorHint="@color/free_ui_dark_gold_text_gray"
                        android:textSize="@dimen/sp_14"/>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/free_ui_dp_48"
                    android:layout_marginTop="@dimen/free_ui_dp_1"
                    android:background="@color/free_ui_white"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/free_ui_dp_12"
                    android:paddingRight="@dimen/free_ui_dp_12">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="3"
                        android:text="批号:"
                        android:textColor="@color/free_ui_glory_theme_color"
                        android:textSize="@dimen/sp_14" />

                    <com.pda.platform.ui.ui_pdaplatform.view.FreeUI_ClearEditText
                        android:layout_width="0dp"
                        android:layout_height="@dimen/free_ui_dp_48"
                        android:layout_weight="7"
                        android:background="@drawable/free_ui_glory_selector_edittext"
                        android:focusable="true"
                        android:hint="请扫描批号"
                        android:textColor="@color/free_ui_dark_gold_text_black"
                        android:textColorHint="@color/free_ui_dark_gold_text_gray"
                        android:textSize="@dimen/sp_14"
                        android:visibility="invisible" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/free_ui_dp_48"
                    android:layout_marginTop="@dimen/free_ui_dp_1"
                    android:background="@color/free_ui_white"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/free_ui_dp_12"
                    android:paddingRight="@dimen/free_ui_dp_12">

                    <com.pda.platform.ui.ui_pdaplatform.view.FreeUI_ClearEditText
                        android:id="@+id/etLot"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/free_ui_dp_48"
                        android:layout_weight="7"
                        android:background="@drawable/free_ui_glory_selector_edittext"
                        android:focusable="true"
                        android:hint="请扫描批号"
                        android:textColor="@color/free_ui_dark_gold_text_black"
                        android:textColorHint="@color/free_ui_dark_gold_text_gray"
                        android:textSize="@dimen/sp_14" />

                    <Button
                        android:id="@+id/btCheckData"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/dp_40"
                        android:layout_marginLeft="@dimen/dp_10"
                        android:layout_weight="3"
                        android:background="@drawable/free_ui_glory_selector_button_click_background"
                        android:text="核对资料"
                        android:textColor="@color/free_ui_white"
                        android:textSize="@dimen/sp_14" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/free_ui_dp_8"
                    android:background="@color/free_ui_glory_theme_color"
                    android:minHeight="@dimen/free_ui_dp_48"
                    android:orientation="horizontal">


                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="载具号"
                        android:textColor="@color/white" />


                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="片数"
                        android:textColor="@color/white" />


                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="雷克码"
                        android:textColor="@color/white" />

                   <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_weight="0.6"
                        android:gravity="center"
                        android:text="->"
                        android:textColor="@color/white"
                        android:singleLine="true"
                        android:ellipsize="end" />


                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="雷克码"
                        android:textColor="@color/white" />


                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="状态"
                        android:textColor="@color/white" />


                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="扣留"
                        android:textColor="@color/white" />


                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="站点"
                        android:textColor="@color/white" />


                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    items="@{dataCheckViewModel.dataItems}"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:itemBinding="@{dataCheckViewModel.durableDataBinding}"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />



                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/free_ui_dp_48"
                    android:background="@color/free_ui_white"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/free_ui_dp_12"
                    android:paddingRight="@dimen/free_ui_dp_12">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="5"
                        android:text="批号:"
                        android:textColor="@color/free_ui_glory_theme_color"
                        android:textSize="@dimen/sp_14" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="5"
                        android:text=""
                        android:textColor="@color/free_ui_glory_theme_color"
                        android:textSize="@dimen/sp_14" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/free_ui_white"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/free_ui_dp_12"
                    android:paddingRight="@dimen/free_ui_dp_12">

                    <TextView
                        android:id="@+id/tvLotID"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="@dimen/free_ui_dp_8"
                        android:layout_weight="5"
                        android:textIsSelectable="true"
                        android:background="@drawable/xxfree_ui_shape_white_right_top_btn_normal"
                        android:padding="@dimen/free_ui_dp_8"
                        android:text="@{dataCheckViewModel.lotEntity.LOT.LOTID}"
                        android:textColor="@color/free_ui_dark_gold_text_black"
                        android:textSize="@dimen/sp_16" />

                    <ImageView
                        android:id="@+id/ivRecipeName"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/free_ui_dp_48"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="@dimen/free_ui_dp_8"
                        android:layout_weight="5"
                        setBarcode="@{dataCheckViewModel.lotEntity.RECIPENAME}"
                        android:singleLine="true" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/free_ui_dp_48"
                    android:background="@color/free_ui_white"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/free_ui_dp_12"
                    android:paddingRight="@dimen/free_ui_dp_12">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="5"
                        android:text="品名:"
                        android:textColor="@color/free_ui_glory_theme_color"
                        android:textSize="@dimen/sp_14" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="5"
                        android:text="载具ID:"
                        android:textColor="@color/free_ui_glory_theme_color"
                        android:textSize="@dimen/sp_14" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/free_ui_white"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/free_ui_dp_12"
                    android:paddingRight="@dimen/free_ui_dp_12">

                    <TextView
                        android:id="@+id/tvPartName"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="@dimen/free_ui_dp_8"
                        android:layout_weight="5"
                        android:background="@drawable/xxfree_ui_shape_white_right_top_btn_normal"
                        android:padding="@dimen/free_ui_dp_8"
                        android:text="@{dataCheckViewModel.lotEntity.OUTPART}"
                        android:textColor="@color/free_ui_dark_gold_text_black"
                        android:textSize="@dimen/sp_16" />

                    <TextView
                        android:id="@+id/tvDurableid"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="@dimen/free_ui_dp_8"
                        android:layout_weight="5"
                        android:background="@drawable/xxfree_ui_shape_white_right_top_btn_normal"
                        android:padding="@dimen/free_ui_dp_8"
                        android:text="@{dataCheckViewModel.lotEntity.LOT.DURABLEID}"
                        android:textColor="@color/free_ui_dark_gold_text_black"
                        android:textSize="@dimen/sp_16" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/free_ui_dp_48"
                    android:background="@color/free_ui_white"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/free_ui_dp_12"
                    android:paddingRight="@dimen/free_ui_dp_12">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="5"
                        android:text="料号:"
                        android:textColor="@color/free_ui_glory_theme_color"
                        android:textSize="@dimen/sp_14" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="5"
                        android:text="站点:"
                        android:textColor="@color/free_ui_glory_theme_color"
                        android:textSize="@dimen/sp_14" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/free_ui_white"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/free_ui_dp_12"
                    android:paddingRight="@dimen/free_ui_dp_12">

                    <TextView
                        android:id="@+id/tvPARTNAME"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="@dimen/free_ui_dp_8"
                        android:layout_weight="5"
                        android:background="@drawable/xxfree_ui_shape_white_right_top_btn_normal"
                        android:padding="@dimen/free_ui_dp_8"
                        android:text="@{dataCheckViewModel.lotEntity.INNERPART}"
                        android:textColor="@color/free_ui_dark_gold_text_black"
                        android:textSize="@dimen/sp_16" />

                    <TextView
                        android:id="@+id/tvStepdesc"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="@dimen/free_ui_dp_8"
                        android:layout_weight="5"
                        android:background="@drawable/xxfree_ui_shape_white_right_top_btn_normal"
                        android:padding="@dimen/free_ui_dp_8"
                        android:text="@{dataCheckViewModel.lotEntity.LOT.STEPDESC}"
                        inputType="textMultLine"
                        android:ellipsize="none"
                        android:textColor="@color/free_ui_dark_gold_text_black"
                        android:textSize="@dimen/sp_16" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/free_ui_dp_48"
                    android:background="@color/free_ui_white"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/free_ui_dp_12"
                    android:paddingRight="@dimen/free_ui_dp_12">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="5"
                        android:text="片数:"
                        android:textColor="@color/free_ui_glory_theme_color"
                        android:textSize="@dimen/sp_14" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="5"
                        android:text="程序:"
                        android:textColor="@color/free_ui_glory_theme_color"
                        android:textSize="@dimen/sp_14" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/free_ui_white"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/free_ui_dp_12"
                    android:paddingRight="@dimen/free_ui_dp_12">

                    <TextView
                        android:id="@+id/tvMAINQTY"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="@dimen/free_ui_dp_8"
                        android:layout_weight="5"
                        android:background="@drawable/xxfree_ui_shape_white_right_top_btn_normal"
                        android:padding="@dimen/free_ui_dp_8"
                        android:text="@{dataCheckViewModel.lotEntity.LOT.MAINQTY}"
                        android:textColor="@color/free_ui_dark_gold_text_black"
                        android:textSize="@dimen/sp_16" />

                    <TextView
                        android:id="@+id/tvProcedureName"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="@dimen/free_ui_dp_8"
                        android:layout_weight="5"
                        android:background="@drawable/xxfree_ui_shape_white_right_top_btn_normal"
                        android:padding="@dimen/free_ui_dp_8"
                        android:text="@{dataCheckViewModel.lotEntity.RECIPENAME}"
                        android:textColor="@color/free_ui_dark_gold_text_black"
                        android:textSize="@dimen/sp_16" />
<!--                    app:setDesc="@{dataCheckViewModel.lotEntity.RECIPENAME}"-->

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/free_ui_dp_48"
                    android:background="@color/free_ui_white"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/free_ui_dp_12"
                    android:paddingRight="@dimen/free_ui_dp_12">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="5"
                        android:text="状态:"
                        android:textColor="@color/free_ui_glory_theme_color"
                        android:textSize="@dimen/sp_14" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="5"
                        android:text="尺寸:"
                        android:textColor="@color/free_ui_glory_theme_color"
                        android:textSize="@dimen/sp_14" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/free_ui_white"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/free_ui_dp_12"
                    android:paddingRight="@dimen/free_ui_dp_12">

                    <TextView
                        android:id="@+id/tvState"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="@dimen/free_ui_dp_8"
                        android:layout_weight="5"
                        android:background="@drawable/xxfree_ui_shape_white_right_top_btn_normal"
                        android:padding="@dimen/free_ui_dp_8"
                        android:text="@{dataCheckViewModel.lotEntity.LOT.STATE}"
                        android:textColor="@color/free_ui_dark_gold_text_black"
                        android:textSize="@dimen/sp_16" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="@dimen/free_ui_dp_8"
                        android:layout_weight="5"
                        android:text="@{dataCheckViewModel.lotEntity.WAFERSIZE}"
                        android:background="@drawable/xxfree_ui_shape_white_right_top_btn_normal"
                        android:padding="@dimen/free_ui_dp_8"
                        android:textColor="@color/free_ui_dark_gold_text_black"
                        android:textSize="@dimen/sp_16" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/free_ui_dp_48"
                    android:background="@color/free_ui_white"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/free_ui_dp_12"
                    android:paddingRight="@dimen/free_ui_dp_12">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="5"
                        android:text="暂停状态:"
                        android:textColor="@color/free_ui_glory_theme_color"
                        android:textSize="@dimen/sp_14" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="5"
                        android:text="外延厚度:"
                        android:textColor="@color/free_ui_glory_theme_color"
                        android:textSize="@dimen/sp_14" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/free_ui_white"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/free_ui_dp_12"
                    android:paddingRight="@dimen/free_ui_dp_12">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="@dimen/free_ui_dp_8"
                        android:layout_weight="5"
                        android:background="@drawable/xxfree_ui_shape_white_right_top_btn_normal"
                        android:padding="@dimen/free_ui_dp_8"
                        android:text="@{dataCheckViewModel.lotEntity.LOT.HOLDSTATE}"
                        android:textColor="@color/free_ui_dark_gold_text_black"
                        android:textSize="@dimen/sp_16" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="@dimen/free_ui_dp_8"
                        android:layout_weight="5"
                        android:text="@{dataCheckViewModel.lotEntity.LOT.EXTENSIONTHICK}"
                        android:background="@drawable/xxfree_ui_shape_white_right_top_btn_normal"
                        android:padding="@dimen/free_ui_dp_8"
                        android:textColor="@color/free_ui_dark_gold_text_black"
                        android:textSize="@dimen/sp_16" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/free_ui_dp_48"
                    android:background="@color/free_ui_white"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/free_ui_dp_12"
                    android:paddingRight="@dimen/free_ui_dp_12">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="5"
                        android:text="recipe描述:"
                        android:textColor="@color/free_ui_glory_theme_color"
                        android:textSize="@dimen/sp_14" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="5"
                        android:text="recipe描述:"
                        android:visibility="gone"
                        android:textColor="@color/free_ui_glory_theme_color"
                        android:textSize="@dimen/sp_14" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/free_ui_white"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/free_ui_dp_12"
                    android:paddingRight="@dimen/free_ui_dp_12">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="@dimen/free_ui_dp_8"
                        android:layout_weight="5"
                        android:background="@drawable/xxfree_ui_shape_white_right_top_btn_normal"
                        android:padding="@dimen/free_ui_dp_8"
                        android:text="@{dataCheckViewModel.lotEntity.RECIPEDESC}"
                        android:textColor="@color/free_ui_dark_gold_text_black"
                        android:textSize="@dimen/sp_16" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="@dimen/free_ui_dp_8"
                        android:layout_weight="5"
                        android:visibility="invisible"
                        android:text="@{dataCheckViewModel.lotEntity.LOT.RECIPEDESC}"
                        android:background="@drawable/xxfree_ui_shape_white_right_top_btn_normal"
                        android:padding="@dimen/free_ui_dp_8"
                        android:textColor="@color/free_ui_dark_gold_text_black"
                        android:textSize="@dimen/sp_16" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/free_ui_dp_8"
                    android:background="@color/free_ui_glory_theme_color"
                    android:minHeight="@dimen/free_ui_dp_48"
                    android:orientation="horizontal">


                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="NO."
                        android:textColor="@color/white" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="批片号"
                        android:textColor="@color/white" />


                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="流水码"
                        android:textColor="@color/white" />


                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="面积"
                        android:textColor="@color/white" />


                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvTechnicalList"
                    items="@{dataCheckViewModel.componentItems}"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:itemBinding="@{dataCheckViewModel.lotDataBinding}"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>
    </LinearLayout>
</layout>