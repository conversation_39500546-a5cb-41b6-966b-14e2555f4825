<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/free_ui_dp_60"
    android:layout_marginTop="24dp"
    android:background="@color/free_ui_glory_theme_color"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/ivBack"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:padding="@dimen/free_ui_dp_12"
        android:scaleType="centerCrop"
        android:src="@drawable/free_ui_blue_sky_selector_left_back"/>

    <LinearLayout
        android:id="@+id/llTitle"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_centerInParent="true"
        android:gravity="center"
        android:orientation="vertical"
        android:paddingLeft="@dimen/free_ui_dp_8"
        android:paddingRight="@dimen/free_ui_dp_8">

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:scaleType="centerCrop"
            android:textSize="@dimen/free_ui_sp_16"
            android:textColor="@color/white"
            android:src="@drawable/free_ui_blue_sky_down_arrow"/>
    </LinearLayout>

    <TextView
        android:id="@+id/tvRight"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:layout_marginRight="@dimen/free_ui_dp_8"
        android:gravity="center"
        android:singleLine="true"
        android:padding="@dimen/free_ui_dp_8"
        android:background="@drawable/free_ui_shape_white_right_top_btn_normal"
        android:textColor="@color/free_ui_blue_sky_selector_title_right_text"
        android:textSize="@dimen/sp_16"
        android:visibility="gone"/>

    <ImageView
        android:id="@+id/ivRight"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:padding="@dimen/free_ui_dp_16"
        android:scaleType="centerCrop"
        android:visibility="gone"/>

</RelativeLayout>
