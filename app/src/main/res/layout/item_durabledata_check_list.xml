<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>

        <variable
            name="dataItem"
            type="com.example.ah_geen_pda.entity.LotEntity" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:gravity="center_vertical"
        android:minHeight="@dimen/free_ui_dp_48"
        android:layout_marginTop="@dimen/free_ui_dp_1"
        android:orientation="horizontal">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@{dataItem.LOT.DURABLEID}"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_14" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@{dataItem.LOT.MAINQTY}"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_14" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@{dataItem.LOT.start}"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_14" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="0.6"
            android:gravity="center"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_14" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@{dataItem.LOT.end}"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_14" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@{dataItem.LOT.STATE}"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_14" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@{dataItem.LOT.HOLDSTATE}"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_14" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@{dataItem.LOT.STEPDESC}"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_14" />

    </LinearLayout>
</layout>