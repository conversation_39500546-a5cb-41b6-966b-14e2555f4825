<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="inventoryViewModel"
            type="com.example.ah_geen_pda.ui.inventory.InventoryViewModel" />
    </data>

    <LinearLayout
        android:id="@+id/llMain"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/free_ui_white"
        android:orientation="vertical">

        <include layout="@layout/activity_title" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_horizontal"
                android:background="@color/free_ui_white"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/free_ui_dp_48"
                    android:layout_marginTop="@dimen/free_ui_dp_1"
                    android:background="@color/free_ui_white"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/free_ui_dp_12"
                    android:paddingRight="@dimen/free_ui_dp_12">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="盘点时间:"
                        android:textColor="@color/free_ui_glory_theme_color"
                        android:textSize="@dimen/sp_14" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="站点:"
                        android:textColor="@color/free_ui_glory_theme_color"
                        android:textSize="@dimen/sp_14" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/free_ui_dp_48"
                    android:layout_marginTop="@dimen/free_ui_dp_1"
                    android:background="@color/free_ui_white"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/free_ui_dp_12"
                    android:paddingRight="@dimen/free_ui_dp_12">

                    <FrameLayout
                        android:id="@+id/flTimeList"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/free_ui_dp_36"
                        android:layout_marginRight="@dimen/dp_4"
                        android:layout_weight="1"
                        android:background="@drawable/free_ui_glory_selector_kuang_button_click_background"
                        android:paddingLeft="@dimen/free_ui_dp_8"
                        android:paddingRight="@dimen/free_ui_dp_8">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <TextView
                                android:id="@+id/tvTime"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:gravity="center_vertical"
                                android:singleLine="true"
                                android:textColor="@color/free_ui_dark_gold_text_black"
                                android:textColorHint="@color/free_ui_dark_gold_text_gray"
                                android:textSize="@dimen/sp_14" />
                        </LinearLayout>

                        <ImageView
                            android:id="@+id/ivTimeArrow"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="right|center_vertical"
                            android:src="@drawable/free_ui_glory_down_arrow" />
                    </FrameLayout>

                    <FrameLayout
                        android:id="@+id/flSiteList"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/free_ui_dp_36"
                        android:layout_marginLeft="@dimen/dp_4"
                        android:layout_weight="1"
                        android:background="@drawable/free_ui_glory_selector_kuang_button_click_background"
                        android:paddingLeft="@dimen/free_ui_dp_8"
                        android:paddingRight="@dimen/free_ui_dp_8">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <TextView
                                android:id="@+id/tvSite"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:gravity="center_vertical"
                                android:singleLine="true"
                                android:textColor="@color/free_ui_dark_gold_text_black"
                                android:textColorHint="@color/free_ui_dark_gold_text_gray"
                                android:textSize="@dimen/sp_14" />
                        </LinearLayout>

                        <ImageView
                            android:id="@+id/ivSiteArrow"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="right|center_vertical"
                            android:src="@drawable/free_ui_glory_down_arrow" />
                    </FrameLayout>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/free_ui_dp_48"
                    android:layout_marginTop="@dimen/free_ui_dp_1"
                    android:background="@color/free_ui_white"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/free_ui_dp_12"
                    android:paddingRight="@dimen/free_ui_dp_12">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="3"
                        android:text="类型:"
                        android:textColor="@color/free_ui_glory_theme_color"
                        android:textSize="@dimen/sp_14" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="3"
                        android:text=""
                        android:textColor="@color/free_ui_glory_theme_color"
                        android:textSize="@dimen/sp_14"
                        android:visibility="invisible" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/free_ui_dp_48"
                    android:layout_marginTop="@dimen/free_ui_dp_1"
                    android:background="@color/free_ui_white"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/free_ui_dp_12"
                    android:paddingRight="@dimen/free_ui_dp_12">

                    <FrameLayout
                        android:id="@+id/flTypeList"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/free_ui_dp_36"
                        android:layout_marginRight="@dimen/dp_4"
                        android:layout_weight="1"
                        android:background="@drawable/free_ui_glory_selector_kuang_button_click_background"
                        android:paddingLeft="@dimen/free_ui_dp_8"
                        android:paddingRight="@dimen/free_ui_dp_8">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <TextView
                                android:id="@+id/tvType"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:gravity="center_vertical"
                                android:singleLine="true"
                                android:textColor="@color/free_ui_dark_gold_text_black"
                                android:textColorHint="@color/free_ui_dark_gold_text_gray"
                                android:textSize="@dimen/sp_14" />
                        </LinearLayout>

                        <ImageView
                            android:id="@+id/ivTypeArrow"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="right|center_vertical"
                            android:src="@drawable/free_ui_glory_down_arrow" />
                    </FrameLayout>

                    <Button
                        android:layout_width="0dp"
                        android:layout_height="@dimen/dp_40"
                        android:layout_marginLeft="@dimen/dp_4"
                        android:layout_weight="1"
                        android:visibility="invisible"
                        android:background="@drawable/free_ui_glory_selector_button_click_background"
                        android:text="查询"
                        android:textColor="@color/free_ui_white"
                        android:textSize="@dimen/sp_14" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/free_ui_dp_48"
                    android:layout_marginTop="@dimen/free_ui_dp_1"
                    android:background="@color/free_ui_white"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/free_ui_dp_12"
                    android:paddingRight="@dimen/free_ui_dp_12">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="3"
                        android:text="waferID:"
                        android:textColor="@color/free_ui_glory_theme_color"
                        android:textSize="@dimen/sp_14" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="7"
                        android:text=""
                        android:textColor="@color/free_ui_glory_theme_color"
                        android:textSize="@dimen/sp_14"
                        android:visibility="invisible" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/free_ui_dp_48"
                    android:layout_marginTop="@dimen/free_ui_dp_1"
                    android:background="@color/free_ui_white"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/free_ui_dp_12"
                    android:paddingRight="@dimen/free_ui_dp_12">

                    <com.pda.platform.ui.ui_pdaplatform.view.FreeUI_ClearEditText
                        android:id="@+id/etWaferID"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/free_ui_dp_48"
                        android:layout_weight="7"
                        android:background="@drawable/free_ui_glory_selector_edittext"
                        android:focusable="true"
                        android:singleLine="true"
                        android:textColor="@color/free_ui_dark_gold_text_black"
                        android:textColorHint="@color/free_ui_dark_gold_text_gray"
                        android:textSize="@dimen/sp_14" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/free_ui_dp_48"
                    android:layout_marginTop="@dimen/free_ui_dp_1"
                    android:background="@color/free_ui_white"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/free_ui_dp_12"
                    android:paddingRight="@dimen/free_ui_dp_12">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="3"
                        android:text="载具ID:"
                        android:textColor="@color/free_ui_glory_theme_color"
                        android:textSize="@dimen/sp_14" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="7"
                        android:text=""
                        android:textColor="@color/free_ui_glory_theme_color"
                        android:textSize="@dimen/sp_14"
                        android:visibility="invisible" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/free_ui_dp_48"
                    android:layout_marginTop="@dimen/free_ui_dp_1"
                    android:background="@color/free_ui_white"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/free_ui_dp_12"
                    android:paddingRight="@dimen/free_ui_dp_12">

                    <com.pda.platform.ui.ui_pdaplatform.view.FreeUI_ClearEditText
                        android:id="@+id/etDurableID"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/free_ui_dp_48"
                        android:layout_weight="7"
                        android:background="@drawable/free_ui_glory_selector_edittext"
                        android:focusable="true"
                        android:singleLine="true"
                        android:textColor="@color/free_ui_dark_gold_text_black"
                        android:textColorHint="@color/free_ui_dark_gold_text_gray"
                        android:textSize="@dimen/sp_14" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/free_ui_dp_4"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/free_ui_dp_48"
                        android:layout_marginTop="@dimen/free_ui_dp_1"
                        android:background="@color/free_ui_white"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingLeft="@dimen/free_ui_dp_12"
                        android:paddingRight="@dimen/free_ui_dp_12">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="当站在制："
                            android:textColor="@color/free_ui_glory_theme_color"
                            android:textSize="@dimen/sp_14" />

                        <TextView
                            android:id="@+id/tvRecipeName"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0"
                            android:textColor="@color/free_ui_dark_gold_text_black"
                            android:textSize="@dimen/sp_14" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/free_ui_dp_48"
                        android:layout_marginTop="@dimen/free_ui_dp_1"
                        android:background="@color/free_ui_white"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingLeft="@dimen/free_ui_dp_12"
                        android:paddingRight="@dimen/free_ui_dp_12">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="当站实盘："
                            android:textColor="@color/free_ui_glory_theme_color"
                            android:textSize="@dimen/sp_14" />

                        <TextView
                            android:id="@+id/tvWaferSize"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0"
                            android:textColor="@color/free_ui_dark_gold_text_black"
                            android:textSize="@dimen/sp_14" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/free_ui_dp_48"
                        android:layout_marginTop="@dimen/free_ui_dp_1"
                        android:background="@color/free_ui_white"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingLeft="@dimen/free_ui_dp_12"
                        android:paddingRight="@dimen/free_ui_dp_12">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="盘到他站："
                            android:textColor="@color/free_ui_glory_theme_color"
                            android:textSize="@dimen/sp_14" />

                        <TextView
                            android:id="@+id/tvRecipeDesc"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0"
                            android:textColor="@color/free_ui_dark_gold_text_black"
                            android:textSize="@dimen/sp_14" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/free_ui_dp_48"
                        android:layout_marginTop="@dimen/free_ui_dp_1"
                        android:background="@color/free_ui_white"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingLeft="@dimen/free_ui_dp_12"
                        android:paddingRight="@dimen/free_ui_dp_12">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="被他站盘到："
                            android:textColor="@color/free_ui_glory_theme_color"
                            android:textSize="@dimen/sp_14" />

                        <TextView
                            android:id="@+id/tvOutPart"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0"
                            android:textColor="@color/free_ui_dark_gold_text_black"
                            android:textSize="@dimen/sp_14" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/free_ui_dp_48"
                        android:layout_marginTop="@dimen/free_ui_dp_1"
                        android:background="@color/free_ui_white"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingLeft="@dimen/free_ui_dp_12"
                        android:paddingRight="@dimen/free_ui_dp_12">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="盘亏："
                            android:textColor="@color/free_ui_glory_theme_color"
                            android:textSize="@dimen/sp_14" />

                        <TextView
                            android:id="@+id/tvOutPqty"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0"
                            android:textColor="@color/free_ui_dark_gold_text_black"
                            android:textSize="@dimen/sp_14" />
                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/free_ui_dp_66"
                    android:layout_marginTop="@dimen/free_ui_dp_1"
                    android:orientation="horizontal"
                    android:visibility="gone">

                    <LinearLayout
                        android:layout_width="@dimen/free_ui_dp_0"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_horizontal"
                        android:layout_weight="1"
                        android:background="@color/free_ui_glory_theme_color"
                        android:orientation="vertical">

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_centerInParent="true"
                                android:text="在制数量"
                                android:textColor="@color/white" />
                        </RelativeLayout>


                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="@dimen/free_ui_dp_0"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:background="@color/free_ui_glory_theme_color"
                        android:orientation="vertical">

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_centerInParent="true"
                                android:text="当站实盘"
                                android:textColor="@color/white" />

                        </RelativeLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="@dimen/free_ui_dp_0"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:background="@color/free_ui_glory_theme_color"
                        android:orientation="vertical">

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_centerInParent="true"
                                android:text="它站实盘"
                                android:textColor="@color/white" />

                        </RelativeLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="@dimen/free_ui_dp_0"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:background="@color/free_ui_glory_theme_color"
                        android:orientation="vertical">

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_centerInParent="true"
                                android:text="盘盈"
                                android:textColor="@color/white" />

                        </RelativeLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="@dimen/free_ui_dp_0"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:background="@color/free_ui_glory_theme_color"
                        android:orientation="vertical">

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_centerInParent="true"
                                android:text="盘亏"
                                android:textColor="@color/white" />

                        </RelativeLayout>
                    </LinearLayout>

                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvTechnicalList"
                    items="@{inventoryViewModel.dataItems}"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:visibility="gone"
                    app:itemBinding="@{inventoryViewModel.dataBinding}"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>
    </LinearLayout>
</layout>