<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="chipFrontSectionOutViewModel"
            type="com.example.ah_geen_pda.ui.chipfrontsectionout.ChipFrontSectionOutViewModel" />
    </data>

    <LinearLayout
        android:id="@+id/llMain"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/free_ui_glory_bg"
        android:orientation="vertical">

        <include layout="@layout/activity_title" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/free_ui_dp_48"
                    android:layout_marginTop="@dimen/free_ui_dp_1"
                    android:background="@color/free_ui_white"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/free_ui_dp_12"
                    android:paddingRight="@dimen/free_ui_dp_12">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="3"
                        android:text="载具号:"
                        android:textColor="@color/free_ui_glory_theme_color"
                        android:textSize="@dimen/sp_14" />

                    <com.pda.platform.ui.ui_pdaplatform.view.FreeUI_ClearEditText
                        android:id="@+id/etDurable"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/free_ui_dp_48"
                        android:layout_weight="7"
                        android:background="@drawable/free_ui_glory_selector_edittext"
                        android:focusable="true"
                        android:hint="请扫描载具号"
                        android:textColor="@color/free_ui_dark_gold_text_black"
                        android:textColorHint="@color/free_ui_dark_gold_text_gray"
                        android:textSize="@dimen/sp_14"/>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/free_ui_dp_48"
                    android:layout_marginTop="@dimen/free_ui_dp_1"
                    android:background="@color/free_ui_white"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/free_ui_dp_12"
                    android:paddingRight="@dimen/free_ui_dp_12">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="3"
                        android:text="批号:"
                        android:textColor="@color/free_ui_glory_theme_color"
                        android:textSize="@dimen/sp_14" />

                    <com.pda.platform.ui.ui_pdaplatform.view.FreeUI_ClearEditText
                        android:id="@+id/etLotID"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/free_ui_dp_48"
                        android:layout_weight="7"
                        android:background="@drawable/free_ui_glory_selector_edittext"
                        android:focusable="true"
                        android:singleLine="true"
                        android:textColor="@color/free_ui_dark_gold_text_black"
                        android:textColorHint="@color/free_ui_dark_gold_text_gray"
                        android:textSize="@dimen/sp_14" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <Button
                        android:id="@+id/btnTrackOut"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_40"
                        android:layout_marginLeft="@dimen/free_ui_dp_40"
                        android:layout_marginTop="20dp"
                        android:layout_marginRight="@dimen/free_ui_dp_40"
                        android:layout_marginBottom="20dp"
                        android:background="@drawable/free_ui_glory_selector_button_click_background"
                        android:text="下机"
                        android:textColor="@color/free_ui_white"
                        android:textSize="@dimen/sp_14" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/free_ui_dp_48"
                    android:layout_marginTop="@dimen/free_ui_dp_1"
                    android:background="@color/free_ui_glory_theme_color"
                    android:orientation="horizontal">


                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="载具ID"
                        android:textColor="@color/white" />


                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="批号"
                        android:textColor="@color/white" />


                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="进站数量"
                        android:textColor="@color/white" />


                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="品名"
                        android:textColor="@color/white" />


                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="工作站"
                        android:textColor="@color/white" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:textColor="@color/white" />


                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvTechnicalList"
                    items="@{chipFrontSectionOutViewModel.trackOutItems}"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:itemBinding="@{chipFrontSectionOutViewModel.trackOutItemBinding}"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />


            </LinearLayout>

        </androidx.core.widget.NestedScrollView>
    </LinearLayout>
</layout>