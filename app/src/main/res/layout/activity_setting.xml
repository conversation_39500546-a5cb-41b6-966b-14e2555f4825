<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>
        <variable
            name="settingViewModel"
            type="com.example.ah_geen_pda.setting.SettingViewModel" />
    </data>

    <LinearLayout
        android:background="@color/free_ui_glory_bg"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <include layout="@layout/activity_title"/>

        <RelativeLayout
            android:id="@+id/rlIP"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/free_ui_dp_12"
            android:background="@drawable/free_ui_selector_white_gray_click"
            android:paddingLeft="@dimen/free_ui_dp_12"
            android:paddingRight="@dimen/free_ui_dp_12">

            <ImageView
                android:id="@+id/ivIp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:src="@drawable/free_ui_glory_more"/>

            <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="@dimen/free_ui_dp_8"
                    android:layout_toLeftOf="@id/ivIp"
                    android:gravity="center_vertical"
                    android:orientation="vertical"
                    android:paddingBottom="@dimen/free_ui_dp_12"
                    android:paddingTop="@dimen/free_ui_dp_12">


                <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="服务器地址"
                        android:textColor="@color/free_ui_glory_theme_color" />

                <TextView
                        android:id="@+id/tvIP"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp_4"
                        android:text="@={settingViewModel.constant.REQUEST_URL_DEBUG}"
                        android:textColor="@color/free_ui_dark_gold_text_black" />
            </LinearLayout>
        </RelativeLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/free_ui_dp_48"
            android:layout_marginTop="@dimen/free_ui_dp_1"
            android:background="@color/free_ui_white"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingLeft="@dimen/free_ui_dp_12"
            android:paddingRight="@dimen/free_ui_dp_12">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="3"
                android:text="版本号"
                android:textColor="@color/free_ui_glory_theme_color"
                android:textSize="@dimen/sp_14" />

            <TextView
                android:id="@+id/tvVersion"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="7"
                android:text="@={settingViewModel.constant.VRESION}"
                android:textColor="@color/free_ui_dark_gold_text_black"
                android:textSize="@dimen/sp_14" />
        </LinearLayout>

    </LinearLayout>
</layout>