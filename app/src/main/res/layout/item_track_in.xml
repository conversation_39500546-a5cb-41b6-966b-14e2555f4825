<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>

        <variable
            name="trackInItem"
            type="com.example.ah_geen_pda.entity.LotEntity" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:gravity="center_vertical"
        android:minHeight="@dimen/free_ui_dp_48"
        android:layout_marginTop="@dimen/free_ui_dp_1"
        android:orientation="horizontal">

        <RelativeLayout
            android:layout_width="@dimen/free_ui_dp_0"
            android:layout_weight="1"
            android:layout_gravity="center"
            android:layout_height="wrap_content">

            <CheckBox
                checkChange="@{trackInItem}"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:checked="@{trackInItem.LOT.showCheck}" />
        </RelativeLayout>

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@{trackInItem.LOT.DURABLEID}"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_14" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@{trackInItem.LOT.LOTID}"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_14" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@{trackInItem.LOT.MAINQTY}"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_14" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@{trackInItem.LOT.PARTNAME}"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_14" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@{trackInItem.LOT.STEPDESC}"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_14" />

    </LinearLayout>
</layout>