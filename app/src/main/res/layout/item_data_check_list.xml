<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>

        <variable
            name="dataItem"
            type="com.example.ah_geen_pda.entity.ComponentList" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:gravity="center_vertical"
        android:minHeight="@dimen/free_ui_dp_48"
        android:layout_marginTop="@dimen/free_ui_dp_1"
        android:orientation="horizontal">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@{String.valueOf(dataItem.post)}"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_14" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@{dataItem.COMPONENTID}"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_14" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@{dataItem.ATTRIBUTE1}"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_14" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@{dataItem.MAINQTY}"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_14" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/free_ui_white"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingLeft="@dimen/free_ui_dp_12"
        android:paddingRight="@dimen/free_ui_dp_12">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="3"
            android:text="批号条码:"
            android:textColor="@color/free_ui_glory_theme_color"
            android:textSize="@dimen/sp_14" />

        <ImageView
            android:layout_width="0dp"
            android:layout_height="@dimen/free_ui_dp_48"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="@dimen/free_ui_dp_8"
            android:layout_weight="7"
            setBarcode="@{dataItem.COMPONENTID}"
            android:singleLine="true" />
    </LinearLayout>

    </LinearLayout>

</layout>