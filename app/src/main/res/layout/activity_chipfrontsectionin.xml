<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="chipFrontSectionInViewModel"
            type="com.example.ah_geen_pda.ui.chipfrontsectionin.ChipFrontSectionInViewModel" />
    </data>

    <LinearLayout
        android:id="@+id/llMain"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/free_ui_glory_bg"
        android:orientation="vertical">

        <include layout="@layout/activity_title" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/free_ui_dp_48"
                    android:layout_marginTop="@dimen/free_ui_dp_1"
                    android:background="@color/free_ui_white"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/free_ui_dp_12"
                    android:paddingRight="@dimen/free_ui_dp_12">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="3"
                        android:text="载具号:"
                        android:textColor="@color/free_ui_glory_theme_color"
                        android:textSize="@dimen/sp_14" />

                    <com.pda.platform.ui.ui_pdaplatform.view.FreeUI_ClearEditText
                        android:id="@+id/etDurable"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/free_ui_dp_48"
                        android:layout_weight="7"
                        android:background="@drawable/free_ui_glory_selector_edittext"
                        android:focusable="true"
                        android:hint="请扫描载具号"
                        android:textColor="@color/free_ui_dark_gold_text_black"
                        android:textColorHint="@color/free_ui_dark_gold_text_gray"
                        android:textSize="@dimen/sp_14"/>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/free_ui_dp_48"
                    android:layout_marginTop="@dimen/free_ui_dp_1"
                    android:background="@color/free_ui_white"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/free_ui_dp_12"
                    android:paddingRight="@dimen/free_ui_dp_12">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="机台:"
                        android:textColor="@color/free_ui_glory_theme_color"
                        android:textSize="@dimen/sp_14" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="批号:"
                        android:textColor="@color/free_ui_glory_theme_color"
                        android:textSize="@dimen/sp_14" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/free_ui_dp_48"
                    android:layout_marginTop="@dimen/free_ui_dp_1"
                    android:background="@color/free_ui_white"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/free_ui_dp_12"
                    android:paddingRight="@dimen/free_ui_dp_12">

                    <com.pda.platform.ui.ui_pdaplatform.view.FreeUI_ClearEditText
                        android:id="@+id/etStepName"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/free_ui_dp_48"
                        android:layout_marginRight="@dimen/dp_4"
                        android:layout_weight="7"
                        android:background="@drawable/free_ui_glory_selector_edittext"
                        android:focusable="true"
                        android:singleLine="true"
                        android:textColor="@color/free_ui_dark_gold_text_black"
                        android:textColorHint="@color/free_ui_dark_gold_text_gray"
                        android:textSize="@dimen/sp_14" />

                    <com.pda.platform.ui.ui_pdaplatform.view.FreeUI_ClearEditText
                        android:id="@+id/etLotID"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/free_ui_dp_48"
                        android:layout_marginLeft="@dimen/dp_4"
                        android:layout_weight="7"
                        android:background="@drawable/free_ui_glory_selector_edittext"
                        android:focusable="true"
                        android:singleLine="true"
                        android:textColor="@color/free_ui_dark_gold_text_black"
                        android:textColorHint="@color/free_ui_dark_gold_text_gray"
                        android:textSize="@dimen/sp_14" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/free_ui_dp_48"
                    android:layout_marginTop="@dimen/free_ui_dp_1"
                    android:background="@color/free_ui_white"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/free_ui_dp_12"
                    android:paddingRight="@dimen/free_ui_dp_12">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="配件:"
                        android:textColor="@color/free_ui_glory_theme_color"
                        android:textSize="@dimen/sp_14" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="配件:"
                        android:textColor="@color/free_ui_glory_theme_color"
                        android:textSize="@dimen/sp_14"
                        android:visibility="invisible" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/free_ui_dp_48"
                    android:layout_marginTop="@dimen/free_ui_dp_1"
                    android:background="@color/free_ui_white"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/free_ui_dp_12"
                    android:paddingRight="@dimen/free_ui_dp_12">

                    <com.pda.platform.ui.ui_pdaplatform.view.FreeUI_ClearEditText
                        android:id="@+id/etPart"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/free_ui_dp_48"
                        android:layout_weight="1"
                        android:background="@drawable/free_ui_glory_selector_edittext"
                        android:focusable="true"
                        android:singleLine="true"
                        android:textColor="@color/free_ui_dark_gold_text_black"
                        android:textColorHint="@color/free_ui_dark_gold_text_gray"
                        android:textSize="@dimen/sp_14" />

                    <Button
                        android:id="@+id/btnTrackIn"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/dp_40"
                        android:layout_marginLeft="@dimen/dp_4"
                        android:layout_weight="1"
                        android:background="@drawable/free_ui_glory_selector_button_click_background"
                        android:text="上机"
                        android:textColor="@color/free_ui_white"
                        android:textSize="@dimen/sp_14" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/free_ui_dp_48"
                    android:layout_marginTop="@dimen/free_ui_dp_1"
                    android:background="@color/free_ui_glory_theme_color"
                    android:orientation="horizontal"
                    android:paddingTop="@dimen/dp_4">

                    <RelativeLayout
                        android:layout_width="@dimen/free_ui_dp_0"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_weight="1">

                        <CheckBox
                            android:id="@+id/allCheck"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerInParent="true" />
                    </RelativeLayout>


                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="载具ID"
                        android:textColor="@color/white" />


                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="批号"
                        android:textColor="@color/white" />


                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="进站数量"
                        android:textColor="@color/white" />


                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="品名"
                        android:textColor="@color/white" />


                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="工作站"
                        android:textColor="@color/white" />
                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvTechnicalList"
                    items="@{chipFrontSectionInViewModel.lotItems}"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:itemBinding="@{chipFrontSectionInViewModel.lotItemBinding}"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/free_ui_dp_48"
                    android:layout_marginTop="@dimen/free_ui_dp_1"
                    android:background="@color/free_ui_white"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/free_ui_dp_12"
                    android:paddingRight="@dimen/free_ui_dp_12">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="3"
                        android:text="批片号:"
                        android:textColor="@color/free_ui_glory_theme_color"
                        android:textSize="@dimen/sp_14" />

                    <com.pda.platform.ui.ui_pdaplatform.view.FreeUI_ClearEditText
                        android:id="@+id/etLotCode"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/free_ui_dp_48"
                        android:layout_weight="7"
                        android:background="@drawable/free_ui_glory_selector_edittext"
                        android:focusable="true"
                        android:hint="请扫描批片号"
                        android:textColor="@color/free_ui_dark_gold_text_black"
                        android:textColorHint="@color/free_ui_dark_gold_text_gray"
                        android:textSize="@dimen/sp_14"/>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/free_ui_dp_1"
                    android:background="@color/free_ui_glory_theme_color"
                    android:minHeight="@dimen/free_ui_dp_48"
                    android:orientation="horizontal">


                    <RelativeLayout
                        android:layout_width="@dimen/free_ui_dp_0"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_weight="1">

                        <CheckBox
                            android:id="@+id/allTechnicalListCheck"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerInParent="true" />
                    </RelativeLayout>

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="批片号"
                        android:textColor="@color/white" />


                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="流水码"
                        android:textColor="@color/white" />


                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="面积"
                        android:textColor="@color/white" />


                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvLotList"
                    items="@{chipFrontSectionInViewModel.componentItems}"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:itemBinding="@{chipFrontSectionInViewModel.lotDataBinding}"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />


            </LinearLayout>

        </androidx.core.widget.NestedScrollView>
    </LinearLayout>
</layout>