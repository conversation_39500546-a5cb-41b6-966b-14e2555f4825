<?xml version="1.0" encoding="utf-8"?>
<!-- 渐变按钮背景 -->
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 按下状态 -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#1976D2"
                android:endColor="#0D47A1"
                android:angle="90" />
            <corners android:radius="12dp" />
        </shape>
    </item>
    
    <!-- 正常状态 -->
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:startColor="@color/free_ui_glory_theme_color"
                android:endColor="#1976D2"
                android:angle="90" />
            <corners android:radius="12dp" />
        </shape>
    </item>
    
</selector>
