<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.example.ah_geen_pda">

    <!-- 添加权限声明 -->
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <application
        android:name=".base.MyApp"
        android:allowBackup="true"
        android:icon="@drawable/log_tb"
        android:label="@string/app_name"
        android:roundIcon="@drawable/log_tb"
        android:configChanges="orientation|screenSize|locale"
        android:networkSecurityConfig="@xml/network_security_config"
        android:supportsRtl="true"
        android:theme="@style/AppTheme">

        <activity
            android:name=".login.LoginActivity"
            android:exported="true"
            android:screenOrientation="portrait">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- 添加 UrgentNotificationService 声明 -->
        <service android:name=".ui.urgentsystem.UrgentNotificationService" />

        <activity android:name=".setting.SettingActivity"
            android:screenOrientation="portrait"/>
        <activity android:name=".MainActivity"
            android:screenOrientation="portrait"/>
        <activity android:name=".ui.datacheck.DataCheckActivity"
            android:screenOrientation="portrait"/>
        <activity android:name=".ui.inventory.InventoryActivity"
            android:screenOrientation="portrait"/>
        <activity android:name=".ui.chipfrontsectionin.ChipFrontSectionInAcitvity"
            android:screenOrientation="portrait"/>
        <activity android:name=".ui.chipfrontsectionout.ChipFrontSectionOutAcitvity"
            android:screenOrientation="portrait"/>
        <activity android:name=".ui.urgentsystem.UrgentSystemActivity"
            android:parentActivityName=".MainActivity"
            android:screenOrientation="portrait"
            android:launchMode="singleTop"/>
        <activity android:name=".ui.countdown.CountdownActivity"
            android:screenOrientation="portrait"/>

        <service
            android:name=".ui.urgentsystem.UrgentTimerService"
            android:enabled="true"
            android:exported="false" />
    </application>

</manifest>