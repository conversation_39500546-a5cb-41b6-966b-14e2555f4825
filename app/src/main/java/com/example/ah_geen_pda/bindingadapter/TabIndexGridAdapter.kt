package com.glorysoft.sunnypda.bindingadapter

import android.graphics.drawable.Drawable
import android.widget.TextView
import androidx.databinding.BindingAdapter

/**
 * FileName: TabIndexGridAdapter
 * Author: XJH
 * Date: 2021/5/13 17:46
 * Description:
 * History:
 */
object TabIndexGridAdapter {

    @BindingAdapter(value = ["compoundDrawableTop"], requireAll = false)
    @JvmStatic
    fun setCompoundDrawableTop(textView: TextView, drawable: Drawable) {
        drawable.setBounds(0, 0, drawable.minimumWidth, drawable.minimumHeight)
        textView.setCompoundDrawables(null, drawable, null, null)
    }

}