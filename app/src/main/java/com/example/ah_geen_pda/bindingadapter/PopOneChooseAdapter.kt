package com.example.ah_geen_pda.bindingadapter

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.example.ah_geen_pda.R


class PopOneChooseAdapter(data: List<String?>?) :
    BaseQuickAdapter<String?, BaseViewHolder>(
        R.layout.item_pop_one_choose_list,
        data as MutableList<String?>?
    ) {
    override fun convert(helper: BaseViewHolder, item: String?) {
        helper.setText(R.id.tvContent, item)
    }
}