package com.glorysoft.sunnypda.bindingadapter

import android.widget.ImageView
import android.widget.TextView
import androidx.databinding.BindingAdapter
import com.example.ah_geen_pda.utils.CodeUtils

object DataCheckImage {

    @BindingAdapter(value = ["setBarcode"], requireAll = false)
    @JvmStatic
    fun setBarcode(imageView: ImageView, recipeName: String) {
        if (recipeName != null && recipeName != "") {
//            val code = recipeName.split("|")[0]
            imageView.setImageBitmap(CodeUtils.createBarcode(recipeName))
        }
    }

    @BindingAdapter(value = ["setDesc"], requireAll = false)
    @JvmStatic
    fun setDesc(textView: TextView, recipeName: String) {
        if (recipeName != null && recipeName != "") {
            val name = recipeName.split("|")[1]
            textView.text = name
        }
    }

}