package com.glorysoft.sunnypda.bindingadapter

import android.graphics.Color
import android.graphics.drawable.Drawable
import android.provider.CalendarContract
import android.widget.CheckBox
import android.widget.CompoundButton
import android.widget.TextView
import androidx.databinding.BindingAdapter
import com.example.ah_geen_pda.entity.ComponentList
import com.example.ah_geen_pda.entity.LotEntity

/**
 * FileName: TabIndexGridAdapter
 * Author: XJH
 * Date: 2021/5/13 17:46
 * Description:
 * History:
 */
object CheckAdapter {

    @BindingAdapter(value = ["checkChange"], requireAll = false)
    @JvmStatic
    fun setCheckChange(checkBox: CheckBox, data: LotEntity) {
        checkBox.setOnCheckedChangeListener { _, isChecked ->
            data.LOT.showCheck = isChecked
        }
    }

    @BindingAdapter(value = ["dataItemCheckChange"], requireAll = false)
    @JvmStatic
    fun setdataItemCheckChange(checkBox: CheckBox, data: ComponentList) {
        checkBox.setOnCheckedChangeListener { _, isChecked ->
            data.mShowCheck = isChecked
        }
    }

}