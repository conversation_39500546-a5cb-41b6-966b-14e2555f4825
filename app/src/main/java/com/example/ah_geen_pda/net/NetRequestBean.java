package com.example.ah_geen_pda.net;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/1/31
 * @desc 网络请求格式
 */
public class NetRequestBean<T> implements Serializable {
    private HeaderBean Header;
    private T Body;

    public HeaderBean getHeader() {
        return Header == null ? new HeaderBean() : Header;
    }

    public void setHeader(HeaderBean header) {
        Header = header;
    }

    public T getBody() {
        return Body;
    }

    public void setBody(T body) {
        Body = body;
    }
}
