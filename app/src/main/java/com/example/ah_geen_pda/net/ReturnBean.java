package com.example.ah_geen_pda.net;

import java.io.Serializable;

/**
 * FileName: ReturnBean
 * Author: XJH
 * Date: 2020/5/25 16:46
 * Description: 返回header
 * History:
 */
public class ReturnBean<T> implements Serializable {

    private HeaderBean Header;
    private HeaderBean RESULT;
    private T Body;

    public HeaderBean getRESULT() {
        return RESULT;
    }

    public void setRESULT(HeaderBean RESULT) {
        this.RESULT = RESULT;
    }

    public HeaderBean getHeader() {
        return Header == null ? new HeaderBean() : Header;
    }

    public void setHeader(HeaderBean header) {
        Header = header;
    }

    public T getBody() {
        return Body;
    }

    public void setBody(T body) {
        Body = body;
    }
}
