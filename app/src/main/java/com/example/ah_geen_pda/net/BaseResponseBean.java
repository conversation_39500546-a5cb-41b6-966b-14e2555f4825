package com.example.ah_geen_pda.net;

import com.example.ah_geen_pda.base.MyApp;
import com.pda.platform.ui.ui_pdaplatform.dialog.FreeUI_GeneralFragmentDialog;
import com.pda.platform.ui.ui_pdaplatform.utils_public.FreeApi_DialogUtils;

import java.io.Serializable;

import androidx.fragment.app.FragmentManager;

public class BaseResponseBean<T> implements Serializable {

    private ReturnBean<T> Response;

    private transient FreeUI_GeneralFragmentDialog tipDialog;


    public ReturnBean<T> getResponse() {
        return Response;
    }

    public void setResponse(ReturnBean<T> response) {
        this.Response = response;
    }


     private void showFailDialog(String message, FragmentManager fragmentManager)
    {
        tipDialog = FreeApi_DialogUtils.getConfirmDialog(MyApp.app, "确定", message, () -> {

        });
        tipDialog.show(fragmentManager,"DIALOG");
    }

}
