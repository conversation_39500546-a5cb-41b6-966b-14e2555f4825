package com.example.ah_geen_pda.net;

import com.example.ah_geen_pda.base.Constant;
import com.pda.platform.ui.ui_pdaplatform.utils_public.FreeApi_DateUtils;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/1/31
 * @desc 网络请求/回复header
 */

public class HeaderBean implements Serializable {

    private String LANGUAGE;

    private String MESSAGENAME;

    private long ORGRRN;

    private String ORGNAME;

    private String TRANSACTIONID;

    private String USERNAME;

    private String RESULT;

    private String RESULTCODE;

    private String RESULTMESSAGE;

    public HeaderBean(String MessageName) {
        this.LANGUAGE = "EN";
        this.MESSAGENAME = MessageName;
        this.TRANSACTIONID = "c5e5e318389d4c39b914e4c10ba519d4";
        this.ORGRRN = 378341;
        this.ORGNAME = "TEST";
        this.USERNAME = Constant.Companion.getCURRENT_USER_NAME();
    }

    public HeaderBean() {
    }

    public String getLANGUAGE() {
        return LANGUAGE == null ? "" : LANGUAGE;
    }

    public void setLANGUAGE(String LANGUAGE) {
        this.LANGUAGE = LANGUAGE;
    }

    public String getMESSAGENAME() {
        return MESSAGENAME == null ? "" : MESSAGENAME;
    }

    public void setMESSAGENAME(String MESSAGENAME) {
        this.MESSAGENAME = MESSAGENAME;
    }

    public long getORGRRN() {
        return ORGRRN;
    }

    public void setORGRRN(long ORGRRN) {
        this.ORGRRN = ORGRRN;
    }

    public String getORGNAME() {
        return ORGNAME == null ? "" : ORGNAME;
    }

    public void setORGNAME(String ORGNAME) {
        this.ORGNAME = ORGNAME;
    }

    public String getTRANSACTIONID() {
        return TRANSACTIONID == null ? "" : TRANSACTIONID;
    }

    public void setTRANSACTIONID(String TRANSACTIONID) {
        this.TRANSACTIONID = TRANSACTIONID;
    }

    public String getUSERNAME() {
        return USERNAME == null ? "" : USERNAME;
    }

    public void setUSERNAME(String USERNAME) {
        this.USERNAME = USERNAME;
    }

    public String getRESULT() {
        return RESULT == null ? "" : RESULT;
    }

    public void setRESULT(String RESULT) {
        this.RESULT = RESULT;
    }

    public String getRESULTCODE() {
        return RESULTCODE == null ? "" : RESULTCODE;
    }

    public void setRESULTCODE(String RESULTCODE) {
        this.RESULTCODE = RESULTCODE;
    }

    public String getRESULTMESSAGE() {
        return RESULTMESSAGE == null ? "" : RESULTMESSAGE;
    }

    public void setRESULTMESSAGE(String RESULTMESSAGE) {
        this.RESULTMESSAGE = RESULTMESSAGE;
    }
}
