package com.example.ah_geen_pda.net;

import java.io.Serializable;


/**
 * <AUTHOR>
 */
public class BaseRequestBean<T> implements Serializable {

    private NetRequestBean<T> Request;

    private NetResponseBean<T> Response;

    public NetRequestBean<T> getRequest() {
        return Request == null ? new NetRequestBean() : Request;
    }

    public void setRequest(NetRequestBean<T> request) {
        Request = request;
    }

    public NetResponseBean<T> getResponse() {
        return Response;
    }

    public void setResponse(NetResponseBean<T> response) {
        Response = response;
    }
}
