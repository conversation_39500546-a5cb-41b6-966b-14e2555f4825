package com.example.ah_geen_pda.entity

import java.io.Serializable

/**
 * 倒计时配置实体类
 * 用于存储从服务器获取的倒计时时间配置信息
 */
data class CountdownTimerEntity(
    /**
     * 第一段倒计时时间（秒）
     */
    var firstStageTime: Int = 0,
    
    /**
     * 第二段倒计时时间（秒），如果为0表示没有第二段
     */
    var secondStageTime: Int = 0,
    
    /**
     * 设备ID
     */
    var equipmentId: String = "",
    
    /**
     * 倒计时类型描述
     */
    var timerType: String = "",
    
    /**
     * 是否启用震动提醒
     */
    var enableVibration: Boolean = true,
    
    /**
     * 是否启用音频提醒
     */
    var enableSound: Boolean = true,
    
    /**
     * 音频资源ID（可选）
     */
    var soundResourceId: Int = 0
) : Serializable {
    
    /**
     * 检查是否有第二段倒计时
     * @return true表示有第二段倒计时，false表示只有一段
     */
    fun hasSecondStage(): Boolean {
        return secondStageTime > 0
    }
    
    /**
     * 获取总倒计时时间
     * @return 总倒计时时间（秒）
     */
    fun getTotalTime(): Int {
        return firstStageTime + secondStageTime
    }
}
