package com.example.ah_geen_pda.entity

import androidx.databinding.BaseObservable
import androidx.databinding.Bindable
import java.io.Serializable

data class LotEntity(
    var LOT: LOTBean = LOTBean(),
    var INNERPART: String = "",
    var OUTPART: String = "",
    var RECIPENAME: String = "",
    var WAFERSIZE: String = "",
    var RECIPEDESC: String = "",
    var STEPATTRIBUTE: String = "",
    var MATERIAL: String = "N"
    ) : Serializable {
}

class LOTBean : Serializable, BaseObservable() {
    var post: Int = 0
    var OBJECTRRN: String = ""
    var ORGRRN: String = ""
    var LOTID: String = ""
    var SUBSTRATEID1: String = ""
    var LOTTYPE: String = ""
    var PARTNAME: String = ""
    var PARTVERSION: String = ""
    var PARTDESC: String = ""
    var CUSTOMERCODE: String = ""
    var MAINQTY: String = ""
    var PROCESSNAME: String = ""
    var PROCESSVERSION: String = ""
    var PROCEDURENAME: String = ""
    var STEPVERSION: String = ""
    var STEPDESC: String = ""
    var COMCLASS: String = ""
    var STATE: String = ""
    var UPDATED: String = ""
    var UPDATEDBY: String = ""
    var HOLDSTATE: String = ""
    var EXTENSIONTHICK: String = ""
    var PRIORITY: String = ""
    var WOID: String = ""
    var REWORKCOUNT: String = ""
    var USECOUNT: String = ""
    var OPERATOR1: String = ""
    var CREATED: String = ""
    var ISSUBLOT: String = ""
    var TRACKINTIME: String = ""
    var TRACKOUTTIME: String = ""
    var LASTEQUIPMENTID: String = ""
    var LOTCOMMENT: String = ""
    var SUBUNITTYPE: String = ""
    var LOCATORID: String = ""
    var PACKAGETYPEQTY: String = ""
    var DURABLELIST: String = ""
    var DURABLEID: String = ""
    var PRODUCTMODEL: String = ""
    var WIRINGNO: String = ""
    var SEALNO: String = ""
    var CHIPNAME: String = ""
    var WIRESPEC: String = ""
    var PACKAGETYPE: String = ""
    var LIMITPACKAGETYPEQTY: String = ""
    var CUSTOMERLOTID: String = ""
    var STEPUPH: String = ""
    var FACTORY: String = ""
    var CHIPID: String = ""
    var EQUIPMENTID: String = ""
    var RECIPEDESC: String = ""
    var start: String = ""
    var end: String = ""
    var  COMPONENTLIST:List<ComponentList> = ArrayList()


    @get:Bindable
    var showCheck: Boolean = false
        set(value) {
            field = value
            notifyChange()
        }
}

class ComponentList : Serializable, BaseObservable(){
    var post: Int = 0
    var OBJECTRRN : String = ""
    var ORGRRN : String = ""
    var COMPONENTID: String = ""
    var COMPONENTALIAS: String = ""
    var PARTNAME: String = ""
    var MAINQTY: String = ""
    var ATTRIBUTE1: String = ""
    @get:Bindable
    var mShowCheck: Boolean = false
        set(value) {
            field = value
            notifyChange()
        }
}