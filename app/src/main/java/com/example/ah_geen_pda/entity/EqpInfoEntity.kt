package com.example.ah_geen_pda.entity

import java.io.Serializable


data class EqpInfoEntity(
    var EQPID: String = "",
    var EQPSTATE: String = "",
    var EQPTYPE: String = "",
    var EQPIP: String = "",
    var FAB: String = "",
    var FABID: String = "",
    var LINE: String = "",
    var LINEID: String = "",
    var POSITION: String = "",
    var POSITIONID: String = "",
    var SHOWNAME: String = "",
    var EQPPORT: String = "",
    var DESC: String = "",
    var NO: String = "",
    var SHIFT: String = "",
    var STEP: String = "",
    var SUPEQPLIST: List<SupEqpEntity> = arrayListOf(),
    var ISHOLD: Int = 0

) : Serializable

data class SupEqpEntity(
    var SUPEQPID : String = "",
    var SUPEQPNAME : String = "",
    var POSITION: String = "",
    var LINE: String = "",
    var NO: String = "",
    var SHOWNAME: String = "",
    var FAB: String = "",
) : Serializable