package com.example.ah_geen_pda.entity

import java.io.Serializable

data class LoginEntity(
        var LOGINRESULT : String = "",
        var USER: UserEntity = UserEntity(),

) : Serializable {
}
data class UserEntity(
        var ORGNAME : String = "",
        var USERNAME : String = "",
        var DESCRIPTION : String = "",
        var DEFAULTLANGUAGE : String = "",
        var ISCLIENTENCRY : String = "",
        var DEFAULTORGRRN : String = "",
        var TOKEN : String = "",
) : Serializable
