package com.example.ah_geen_pda.base

import androidx.databinding.BaseObservable
import androidx.databinding.Bindable
import com.example.ah_geen_pda.BR

class Constant {

    companion  object : BaseObservable() {
        @get:Bindable
//        var REQUEST_URL_DEBUG: String = "http://10.10.121.251:8080/esbrest/RestService/postrequest" //测试区接口地址
//        @get:Bindable
        var REQUEST_URL_DEBUG: String = "http://10.10.50.43:8080/esbrest/RestService/postrequest" //正式区接口地址
            set(value) {
                field = value
                // 只更新本字段
                notifyPropertyChanged(BR.rEQUEST_URL_DEBUG)

                // 更新所有字段
//                notifyChange()
            }

        @get:Bindable
        var FILE_URL: String = "http://**************:8000/PDADEBUG/Version.txt" //APK下载地址
//            set(value) {
//                field = value
//                notifyPropertyChanged(BR.fILE_URL)
//            }

        var CURRENT_USER: String = ""
        var CURRENT_USER_NAME: String = ""

        var IMEI_ID : String = ""

        var IP_ADDRESS : String = ""

        var VRESION: String = "1.0"

        var AUTHORITYLIST : ArrayList<String> = arrayListOf()

        @get:Bindable
        var LANGUAGE: String = "中文" //接口地址
            set(value) {
                field = value
                // 只更新本字段
                notifyPropertyChanged(BR.rEQUEST_URL_DEBUG)

                // 更新所有字段
//                notifyChange()
            }
    }
}