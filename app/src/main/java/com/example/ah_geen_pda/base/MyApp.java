 package com.example.ah_geen_pda.base;

 import android.app.Application;
 import android.content.Context;
 import com.pda.platform.ui.ui_pdaplatform.base.FreeUI_InitUtils;
 import com.pda.platform.ui.ui_pdaplatform.utils_public.FreeApi_StaticMembers;

 import java.io.File;
 import java.util.concurrent.TimeUnit;

 import okhttp3.OkHttpClient;
 import rxhttp.wrapper.cookie.CookieStore;
 import rxhttp.wrapper.param.RxHttp;
 import rxhttp.wrapper.ssl.HttpsUtils;


public class MyApp extends Application {

    public static Context app;
    private static MyApp instance;

    public static MyApp getInstance() {
        return instance;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        instance = this;
        app = getApplicationContext();
        FreeUI_InitUtils.init(getApplicationContext(), this, true,60*1000L,0);
        FreeUI_InitUtils.setTheme(FreeApi_StaticMembers.THEME_GLORY);
        initRxHttp();

    }

     private void initRxHttp() {
         File file = new File(getExternalCacheDir(), "RxHttpCookie");
         HttpsUtils.SSLParams sslParams = HttpsUtils.getSslSocketFactory();
         OkHttpClient client = new OkHttpClient.Builder()
                 .cookieJar(new CookieStore(file))
                 .connectTimeout(30, TimeUnit.SECONDS)
                 .readTimeout(30, TimeUnit.SECONDS)
                 .writeTimeout(30, TimeUnit.SECONDS)
                 .sslSocketFactory(sslParams.sSLSocketFactory, sslParams.trustManager) //添加信任证书
                 .hostnameVerifier((hostname, session) -> true) //忽略host验证
                 .build();
         //RxHttp初始化，自定义OkHttpClient对象，非必须
         RxHttp.init(client);
         RxHttp.setDebug(true);
         RxHttp.setOnParamAssembly(p -> {
            /*根据不同请求添加不同参数，子线程执行，每次发送请求前都会被回调
            如果希望部分请求不回调这里，发请求前调用Param.setAssemblyEnabled(false)即可
             */
             return p; //添加公共请求头
         });
     }
 }
