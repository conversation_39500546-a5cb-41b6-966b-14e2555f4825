package com.example.ah_geen_pda.base

import android.os.Bundle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.rxLifeScope
import com.glorysoft.sunnypda.entity.ui.CallObserveBean
import com.example.ah_geen_pda.entity.ToastMessageBean
import com.example.ah_geen_pda.utils.SingleLiveEvent
import com.example.ah_geen_pda.utils.show
import com.rxjava.rxlife.ScopeViewModel
import kotlinx.coroutines.CoroutineScope

open class BaseViewModel :  ScopeViewModel(MyApp.getInstance()),LifecycleObserver {

    val defUI: UIChange by lazy { UIChange() }

    fun launchWithError(
            block: suspend CoroutineScope.() -> Unit,
            error: () -> Unit,
            isShowDialog: Boolean = true
    ) {
        rxLifeScope.launch({
            block()
        }, {
            it.show()
            error()
        }, {
            if (isShowDialog) defUI.showDialog.call()
        }, {
            defUI.dismissDialog.call()
            defUI.complete.call()
        })
    }

    /**
     * 启动一个仅关注结果的协程任务
     *
     * 该函数旨在简化协程的使用，通过封装通用的协程启动逻辑和UI相关的处理，
     * 使得调用者可以专注于实现自己的业务逻辑，而无需重复处理加载对话框和异常等情况。
     *
     * @param block 协程中的业务逻辑代码块
     * @param isShowDialog 是否显示加载对话框，考虑到性能和用户体验，默认为 true
     */
    fun launchOnlyResult(
            block: suspend CoroutineScope.() -> Unit,
            isShowDialog: Boolean = true
    ) {
        // 使用RxLifeScope管理协程生命周期，确保在UI组件销毁时，协程能够被正确取消，避免内存泄漏
        rxLifeScope.launch({
            block()
        }, {
            // 处理协程执行过程中的异常
            it.show()
        }, {
            // 根据参数决定是否显示加载对话框
            if (isShowDialog) defUI.showDialog.call()
        }, {
            // 任务完成后，确保加载对话框被关闭，并执行完成回调
            defUI.dismissDialog.call()
            defUI.complete.call()
        })
    }

    fun launchWithComplete(
            block: suspend CoroutineScope.() -> Unit,
            complete: () -> Unit,
            isShowDialog: Boolean = true
    ) {
        rxLifeScope.launch({
            block()
        }, {
            it.show()
        }, {
            if (isShowDialog) defUI.showDialog.call()
        }, {
            defUI.dismissDialog.call()
            defUI.complete.call()
            complete()
        })
    }

    fun launchWithErrorAndComplete(
            block: suspend CoroutineScope.() -> Unit,
            error: () -> Unit={},
            complete: () -> Unit={},
            isShowDialog: Boolean = true
    ) {
        rxLifeScope.launch({
            block()
        }, {
            it.show()
            error()
        }, {
            if (isShowDialog) defUI.showDialog.call()
        }, {
            defUI.dismissDialog.call()
            defUI.complete.call()
            complete()
        })
    }

    /**
     * UI变更管理类，用于封装各种UI事件的处理
     */
    inner class UIChange {
        // 使用懒加载初始化，用于触发显示对话框的事件，携带字符串消息
        val showDialog by lazy { SingleLiveEvent<String>() }

        // 使用懒加载初始化，用于触发关闭对话框的事件，不携带消息
        val dismissDialog by lazy { SingleLiveEvent<Void>() }

        // 使用懒加载初始化，用于触发Toast消息显示的事件，携带ToastMessageBean对象
        val toastEvent by lazy { SingleLiveEvent<ToastMessageBean>() }

        // 使用懒加载初始化，用于表示某个操作完成的事件，不携带消息
        val complete by lazy { SingleLiveEvent<Void>() }

        // 使用懒加载初始化，用于触发启动新Activity的事件，携带Bundle对象作为参数
        val startActivity by lazy { SingleLiveEvent<Bundle>() }

        // 使用懒加载初始化，用于触发调用观察者的事件，携带CallObserveBean对象
        val callObserve by lazy { SingleLiveEvent<CallObserveBean>() }
    }
}