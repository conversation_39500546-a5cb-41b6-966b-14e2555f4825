package com.example.ah_geen_pda.base

import android.util.Log
import com.example.ah_geen_pda.net.BaseRequestBean
import com.example.ah_geen_pda.net.HeaderBean
import com.example.ah_geen_pda.net.NetRequestBean
import com.google.gson.Gson
import com.google.gson.JsonObject
import com.pda.platform.ui.ui_pdaplatform.utils_public.FreeApi_LogUtils
import org.apache.commons.lang3.StringEscapeUtils
import java.util.*

/**
 * <AUTHOR>
 * @date 2021/4/21
 * @desc
 */
open class BaseRepository {
    /**
     * 将给定的JSON对象和消息名称封装为一个用于网络请求的Map对象
     * 此函数主要用于准备网络请求所需的数据结构，将请求的JSON数据和相关配置信息
     * 封装到一个Map对象中，以便进行后续的网络通信
     *
     * @param map 包含请求具体数据的JSON对象
     * @param messageName 请求的消息名称，用于标识请求类型
     * @return 返回一个包含请求所有必要信息的Map对象，包括发送者ID、请求消息和超时设置
     */
    protected fun getInJsonData(map: JsonObject,messageName : String): Map<String,String> {
        // 创建一个泛型为Any?的网络请求Bean对象，用于封装请求的主体内容
        val format: NetRequestBean<*> = NetRequestBean<Any?>()
        format.body =map
        // 创建一个消息头对象，包含消息名称，用于标识请求类型
        format.header = HeaderBean(messageName)

        // 创建一个泛型为Any?的基础请求Bean对象，用于封装整个请求信息
        val message: BaseRequestBean<*> = BaseRequestBean<Any?>()
        message.request = format

        // 初始化一个可变Map对象，用于存储请求的各种配置信息
        val map: MutableMap<String, String> = HashMap()
        // 设置发送者ID，固定值为"EAP2MESSender"
        map["senderId"] = "EAP2MESSender"
        // 将请求消息对象转换为JSON字符串并进行转义处理后，存储到Map中
        map["requestMessage"] = StringEscapeUtils.unescapeJson(Gson().toJson(message))
        // 设置请求超时时间为5000毫秒
        map["timeOut"] = "5000"

        // 在日志中输出请求的JSON信息，用于调试和记录
        Log.e("请求JSON", "getInJsonData: " + StringEscapeUtils.unescapeJson(Gson().toJson(message)))
        // 保存请求信息到日志文件，便于后续追踪和分析
        FreeApi_LogUtils.saveErrorMessages(StringEscapeUtils.unescapeJson(Gson().toJson(message)),"请求JSON")

        // 返回封装好的Map对象，包含所有请求相关信息
        return map
    }
}