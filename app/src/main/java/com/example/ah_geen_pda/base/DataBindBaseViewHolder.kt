package com.example.ah_geen_pda.base

import android.view.View
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import com.chad.library.adapter.base.BaseViewHolder

/**
 * Created by YJ on 2020/9/18
 */
class DataBindBaseViewHolder(view: View) : BaseViewHolder(view) {
    private var baseBind :ViewDataBinding?=null

    init {
        baseBind = DataBindingUtil.bind(itemView)
    }

    fun getDataBinding(): ViewDataBinding? {
        return baseBind
    }
}