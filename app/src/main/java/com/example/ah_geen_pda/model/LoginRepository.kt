package com.example.ah_geen_pda.model

import com.example.ah_geen_pda.base.BaseRepository
import com.example.ah_geen_pda.base.Constant
import com.example.ah_geen_pda.entity.LoginEntity
import com.google.gson.JsonObject
import rxhttp.wrapper.param.RxHttp
import rxhttp.wrapper.param.toResponse


class LoginRepository : BaseRepository() {

    suspend fun getLogin(map: JsonObject): LoginEntity {
        return RxHttp.postForm(Constant.REQUEST_URL_DEBUG)
            .addAll(getInJsonData(map,"SYS.USER_LOGIN"))
            .toResponse<LoginEntity>()
            .await()
    }

}