package com.example.ah_geen_pda.model

import androidx.databinding.ObservableArrayList
import com.example.ah_geen_pda.base.BaseRepository
import com.example.ah_geen_pda.base.Constant
import com.example.ah_geen_pda.entity.CountdownTimerEntity
import com.example.ah_geen_pda.entity.LoginEntity
import com.example.ah_geen_pda.entity.LotEntity
import com.example.ah_geen_pda.entity.UrgentFilmInformation
import com.google.gson.JsonObject
import rxhttp.wrapper.param.RxHttp
import rxhttp.wrapper.param.toResponse


class LotInfoRepository : BaseRepository() {

    suspend fun getLotInfo(map: JsonObject): LotEntity {
        return RxHttp.postForm(Constant.REQUEST_URL_DEBUG)
            .addAll(getInJsonData(map,"GN.LOTQUERY"))
            .toResponse<LotEntity>()
            .await()
    }

    suspend fun getLotInfoTo(map: JsonObject): LotEntity {
        return RxHttp.postForm(Constant.REQUEST_URL_DEBUG)
            .addAll(getInJsonData(map,"GN.LOTQUERY_TO"))
            .toResponse<LotEntity>()
            .await()
    }

    /**
     * 进站
     */
    suspend fun defaultTrackIn(map: JsonObject): Any {
        return RxHttp.postForm(Constant.REQUEST_URL_DEBUG)
            .addAll(getInJsonData(map,"GN.LOTTRACKIN"))
            .toResponse<Any>()
            .await()
    }

    suspend fun defaultTrackInTo(map: JsonObject): Any {
        return RxHttp.postForm(Constant.REQUEST_URL_DEBUG)
            .addAll(getInJsonData(map,"GN.LOTTRACKIN_TO"))
            .toResponse<Any>()
            .await()
    }

    /**
     * 出站
     */
    suspend fun defaultTrackOut(map: JsonObject): Any {
        return RxHttp.postForm(Constant.REQUEST_URL_DEBUG)
            .addAll(getInJsonData(map,"GN.LOTTRACKOUT"))
            .toResponse<Any>()
            .await()
    }


    suspend fun getUrgentFilmInformation(site:String): ObservableArrayList<UrgentFilmInformation> {
        return RxHttp.postJson("http://10.10.50.43:8080/interfacerest/RestService/getUrgentFilmInformation")
            .addAll("{\"site\":"+site+"}")  // 使用 addJsonParams 方法
            .toResponse<ObservableArrayList<UrgentFilmInformation>>()
            .await()

//        return RxHttp.postJson("https://mock.jsont.run/KCXOGC1Sd5VCDG9e4C0Nn")
//            .addAll("{\"site\":"+site+"}")  // 使用 addJsonParams 方法
//            .toResponse<ObservableArrayList<UrgentFilmInformation>>()
//            .await()
    }

}