package com.example.ah_geen_pda.model

import com.example.ah_geen_pda.base.BaseRepository
import com.example.ah_geen_pda.base.Constant
import com.example.ah_geen_pda.entity.LoginEntity
import com.example.ah_geen_pda.entity.LotInventoryEntity
import com.google.gson.JsonObject
import rxhttp.wrapper.param.RxHttp
import rxhttp.wrapper.param.toResponse


class InventoryRepository : BaseRepository() {

    suspend fun getLotInventory(map: JsonObject): LotInventoryEntity {
        return RxHttp.postForm(Constant.REQUEST_URL_DEBUG)
            .addAll(getInJsonData(map,"GN.LOTINVENTORY"))
            .toResponse<LotInventoryEntity>()
            .await()
    }

}