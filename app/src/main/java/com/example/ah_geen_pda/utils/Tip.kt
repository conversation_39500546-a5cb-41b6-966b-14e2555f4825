package com.example.ah_geen_pda.utils

import android.os.Handler
import android.os.Looper
import android.widget.Toast
import com.example.ah_geen_pda.base.MyApp
import com.pda.platform.ui.ui_pdaplatform.utils_public.FreeApi_ToastUtils

/**
 * 可在任意线程执行本类方法
 */
object Tip {
    private val mHandler = Handler(Looper.getMainLooper())
    private var mToast: Toast? = null

    @JvmOverloads
    fun show(msgResId: Int, timeLong: Boolean = false) {
        show(MyApp.getInstance().getString(msgResId), timeLong)
    }

    @JvmOverloads
    fun show(msg: CharSequence?, timeLong: Boolean = false) {
        runOnUiThread {
            if (mToast != null) {
                mToast!!.cancel()
            }
            val duration = if (timeLong) Toast.LENGTH_LONG else Toast.LENGTH_SHORT
            FreeApi_ToastUtils.showFreeToast(msg.toString(), MyApp.getInstance(), false, duration)
        }
    }

    private fun runOnUiThread(runnable: Runnable) {
        if (Looper.getMainLooper() == Looper.myLooper()) {
            runnable.run()
        } else {
            mHandler.post(runnable)
        }
    }
}