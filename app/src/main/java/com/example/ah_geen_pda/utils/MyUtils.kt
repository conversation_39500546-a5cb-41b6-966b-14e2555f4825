package com.example.ah_geen_pda.utils

import android.content.Context
import android.view.KeyEvent
import android.view.inputmethod.EditorInfo
import android.widget.EditText
import android.widget.TextView.OnEditorActionListener
import com.pda.platform.ui.ui_pdaplatform.entity.FreeUI_TabIndexGridEntity


object MyUtils {
    fun setPicToDrawable(context: Context, entity: List<FreeUI_TabIndexGridEntity>): List<FreeUI_TabIndexGridEntity> {
        for (og in entity) {
            if (og.picName.isNotEmpty()) {
                val drawable = context.getDrawable(context.resources
                    .getIdentifier(og.picName, "drawable", context.packageName))
                og.drawable = drawable
            }
        }
        return entity
    }

    fun editActionListener(editText: EditText, listener: EditTextActionListener) {
        editText.setOnEditorActionListener { textView, actionId, keyEvent -> //当actionId == XX_SEND 或者 XX_DONE时都触发
            //或者event.getKeyCode == ENTER 且 event.getAction == ACTION_DOWN时也触发
            if (actionId == EditorInfo.IME_ACTION_SEND || actionId == EditorInfo.IME_ACTION_NEXT || actionId == EditorInfo.IME_ACTION_DONE || keyEvent != null && KeyEvent.KEYCODE_ENTER == keyEvent.keyCode && KeyEvent.ACTION_DOWN == keyEvent.action) {
                //处理事件
                listener.onKeyDown(editText.text.toString().trim { it <= ' ' })
            }
            false
        }
    }

    interface EditTextActionListener {
        fun onKeyDown(s: String?)
    }

    /**
     * 点击Enter，文本小写转大写
     *
     * @param editText
     */
    fun initEnterToUpperCase(editText: EditText) {
        initEnterToUpperCase(editText, null)
    }

    fun initEnterToUpperCase(
        editText: EditText,
        onEnterKeyListener: EditTextActionListener?
    ) {
        editText.imeOptions = EditorInfo.IME_ACTION_GO
        editText.isSingleLine = true
        editText.setOnEditorActionListener(OnEditorActionListener { textView, i, keyEvent ->
            if (EditorInfo.IME_ACTION_GO == i) {
                val text = editText.text.toString()
                onEnterKeyListener?.onKeyDown(text)
                //转大写并触发扫描效果
                editText.text.clear()
                editText.setText(text.toUpperCase())
                editText.setSelection(editText.text.length)
                //                nextEditTextFocus(editText);      跳转下一个
                return@OnEditorActionListener true
            }
            false
        })
    }
}