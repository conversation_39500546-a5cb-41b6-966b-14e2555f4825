package com.example.ah_geen_pda.utils

import android.content.Context
import android.media.AudioAttributes
import android.media.MediaPlayer
import android.os.Vibrator

object VibrationAndMusicUtils {
    private var vibrator: Vibrator? = null
    private var mediaPlayer: MediaPlayer? = null

    fun startVibration(context: Context) {
        vibrator = context.getSystemService(Context.VIBRATOR_SERVICE) as? Vibrator
        if (vibrator?.hasVibrator() == true) {
            vibrator?.vibrate(longArrayOf(0, 1000, 1000, 1000), 0)
        }
    }

    fun stopVibration() {
        vibrator?.cancel()
    }

    fun startMusic(context: Context, resourceId: Int) {
        val audioAttributes = AudioAttributes.Builder()
            .setUsage(AudioAttributes.USAGE_NOTIFICATION)
            .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC) // 修改为合法的常量
            .build()

        mediaPlayer?.release()
        mediaPlayer = MediaPlayer.create(context, resourceId).apply {
            setAudioAttributes(audioAttributes)
            isLooping = true
            start()
        }
    }

    fun stopMusic() {
        mediaPlayer?.apply {
            stop()
            release()
        }
        mediaPlayer = null
    }
}
