package com.example.ah_geen_pda.utils

import android.content.Context
import android.graphics.drawable.ColorDrawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.Animation
import android.view.animation.RotateAnimation
import android.widget.ImageView
import android.widget.PopupWindow
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.ah_geen_pda.R
import com.example.ah_geen_pda.bindingadapter.PopOneChooseAdapter


object PopWindowsUtils {
    fun getOneChoosePop(
        context: Context, width: Int, list: List<String>, ivArrow: ImageView,
        textAndPositionCallback: PopTextAndPositionCallback
    ): GeneralPop {
        val mView: View = LayoutInflater.from(context)
            .inflate(R.layout.pop_string_list, null)
        val recyclerView: RecyclerView =
            mView.findViewById(R.id.rvList)
        recyclerView.layoutManager = LinearLayoutManager(context)
        val generalPop = GeneralPop(
            context, mView, width, ViewGroup.LayoutParams.WRAP_CONTENT
        )
        val popListAdapter = PopOneChooseAdapter(list)
        popListAdapter.setOnItemClickListener { adapter, _, position ->
            textAndPositionCallback.onSuccess(
                java.lang.String.valueOf(adapter.getItem(position)),
                position
            )
            generalPop.dismiss()
        }
        recyclerView.adapter = popListAdapter
        generalPop.isOutsideTouchable = true
        generalPop.setOnDismissListener { ivArrow?.let { setArrowMiss(it) } }
        return generalPop
    }

    interface PopTextAndPositionCallback {
        fun onSuccess(string: String, potion: Int)
    }

    interface PopManyChooseCallback{
        fun onChecked(string: String?,potion: Int)
        fun onUnChecked(string: String?,potion: Int)
    }

    private fun setArrowMiss(view: View) {
        val animationDismiss = RotateAnimation(
            180f, 0f, Animation.RELATIVE_TO_SELF,
            0.5f, Animation.RELATIVE_TO_SELF, 0.5f
        )
        animationDismiss.duration = 500
        animationDismiss.fillAfter = true
        view.startAnimation(animationDismiss)
    }

    fun setArrowShow(view: View) {
        val animation = RotateAnimation(
            0f, 180f, Animation.RELATIVE_TO_SELF,
            0.5f, Animation.RELATIVE_TO_SELF, 0.5f
        )
        animation.duration = 500
        animation.fillAfter = true
        view.startAnimation(animation)
    }

    class GeneralPop(context: Context?, mView: View?, width: Int, height: Int) :
        PopupWindow(context) {
        init {
            //设置PopupWindow的View
            this.contentView = mView
            //设置PopupWindow弹出窗体的宽
            this.width = width
            //设置PopupWindow弹出窗体的高
            this.height = height
            //设置PopupWindow弹出窗体可点击
            this.isFocusable = true
            this.isOutsideTouchable = false
            //设置SelectPicPopupWindow弹出窗体动画效果
            this.animationStyle = R.style.PopupWindowBottomAnimation
            //实例化一个ColorDrawable颜色为半透明
            val dw = ColorDrawable(-0x50000000)
            //设置SelectPicPopupWindow弹出窗体的背景
            setBackgroundDrawable(dw)
        }
    }
}