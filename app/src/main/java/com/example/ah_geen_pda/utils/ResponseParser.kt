package com.example.ah_geen_pda.utils

import android.text.TextUtils
import com.example.ah_geen_pda.net.BaseResponseBean
import rxhttp.wrapper.annotation.Parser
import rxhttp.wrapper.entity.ParameterizedTypeImpl
import rxhttp.wrapper.exception.ParseException
import rxhttp.wrapper.parse.AbstractParser
import rxhttp.wrapper.utils.convert
import java.io.IOException
import java.lang.reflect.Type

/**
 * 输入T,输出T,并对code统一判断
 *
 * 如果使用协程发送请求，wrappers属性可不设置，设置了也无效
 */
@Parser(name = "Response")
open class ResponseParser<T> : AbstractParser<T> {
    /**
     * 此构造方法适用于任意Class对象，但更多用于带泛型的Class对象，如：List<Student>
     *
     * 用法:
     * Java: .asParser(new ResponseParser<List<Student>>(){})
     * Kotlin: .asParser(object : ResponseParser<List<Student>>() {})
     *
     * 注：此构造方法一定要用protected关键字修饰，否则调用此构造方法将拿不到泛型类型
     */
    protected constructor() : super()

    /**
     * 此构造方法仅适用于不带泛型的Class对象，如: Student.class
     *
     * 用法
     * Java: .asParser(new ResponseParser<>(Student.class))   或者  .asResponse(Student.class)
     * Kotlin: .asParser(ResponseParser(Student::class.java)) 或者  .asResponse<Student>()
     */
    constructor(type: Type) : super(type)

    /**
     * 当解析响应时调用此方法
     *
     * @param response okhttp3.Response对象，代表从服务器接收到的响应
     * @return T 解析后的数据，类型由调用时指定的泛型决定
     * @throws IOException 如果在读取响应过程中发生I/O错误
     *
     * 此方法负责解析给定的响应对象，将其转换为指定的类型T
     * 它首先检查响应的头信息，以确定是否成功如果成功，它尝试提取并返回响应体中的数据
     * 如果响应不成功或数据为空，它会根据具体情况抛出ParseException异常
     */
    @Throws(IOException::class)
    override fun onParse(response: okhttp3.Response): T {
        // 获取泛型类型
        val type: Type = ParameterizedTypeImpl[BaseResponseBean::class.java, mType]
        // 将响应转换为指定的类型
        val data: BaseResponseBean<T> = response.convert(type)
        var t = data as T
        // 检查响应结果是否成功
        if (data.response.header.result != "SUCCESS" ) {
            // 如果响应中包含错误消息，抛出ParseException异常
            if (!TextUtils.isEmpty(data.response.header.messagename)) {
                throw ParseException(data.response.header.result, data.response.header.messagename, response)
            }else{
                // 如果没有错误消息，使用结果代码作为错误信息抛出ParseException异常
                throw ParseException(data.response.header.result, data.response.header.resultcode.toString(), response)
            }
        }
        // 如果响应体为空且泛型为String类型，返回结果消息
        if (data.response.body == null && mType === String::class.java) {
            /*
             * 考虑到有些时候服务端会返回：{"errorCode":0,"errorMsg":"关注成功"}  类似没有data的数据
             * 此时code正确，但是data字段为空，直接返回data的话，会报空指针错误，
             * 所以，判断泛型为String类型时，重新赋值，并确保赋值不为null
             */
            @Suppress("UNCHECKED_CAST")
            t = data.response.header.resultmessage as T
        }else{
            // 获取data字段
            t = data.response.body
        }
        // 返回解析后的数据
        return t
    }
}