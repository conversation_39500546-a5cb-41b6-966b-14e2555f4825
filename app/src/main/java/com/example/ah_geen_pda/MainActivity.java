package com.example.ah_geen_pda;

import android.content.Intent;
import android.os.Bundle;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.example.ah_geen_pda.base.Constant;
import com.example.ah_geen_pda.base.DataBindBaseViewHolder;
import com.example.ah_geen_pda.databinding.ItemMainBinding;
import com.example.ah_geen_pda.utils.MyUtils;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.pda.platform.ui.ui_pdaplatform.entity.FreeUI_TabIndexGridEntity;
import com.pda.platform.ui.ui_pdaplatform.utils_public.FreeApi_StringUtils;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.RecyclerView;

public class MainActivity extends AppCompatActivity {

    private List<FreeUI_TabIndexGridEntity> mfgItems;
    private MainAdapter mAdapter;


    /**
     * 初始化界面组件和数据
     * 该方法在活动创建时调用，用于设置界面布局，初始化RecyclerView和适配器，并设置点击事件监听器
     *
     * @param savedInstanceState 保存的实例状态，如果活动被重新创建（如屏幕旋转），则可以使用此对象来恢复之前的状态
     */
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        // 初始化RecyclerView组件
        RecyclerView rvMFGList = findViewById(R.id.rvMFGList);
        // 初始化数据列表
        mfgItems = new ArrayList<>();
        // 创建适配器实例
        mAdapter = new MainAdapter(null);
        // 将适配器设置到RecyclerView上
        rvMFGList.setAdapter(mAdapter);

        // 初始化资源资产
        initAssets();

        // 设置适配器的点击事件监听器
        mAdapter.setOnItemClickListener((adapter, view, position) -> {
            // 创建意图，用于启动新的活动
            Intent intent = new Intent();
            // 获取点击项的数据实体
            FreeUI_TabIndexGridEntity entity = mAdapter.getData().get(position);
            try {
                // 通过类名反射获取类对象
                Class<?> clazz = Class.forName(entity.getSkipClass());
                // 向意图中添加额外数据
                intent.putExtra("showPlugin", entity.getShowPlugin());
                // 设置意图的目标类
                intent.setClass(MainActivity.this, clazz);
                // 启动新的活动
                startActivity(intent);
            } catch (ClassNotFoundException e) {
                // 如果反射获取类失败，则打印异常信息
                e.printStackTrace();
            }

        });

    }

    /**
     * 初始化应用中的资源数据
     * 该方法主要用于读取应用资产目录下的数据文件，并解析这些数据以初始化应用中的资源
     * 它通过读取名为"mfg.txt"的文件，解析其中的JSON数据，并将其转换为应用所需的实体对象列表
     * 最后，这些数据被用来更新适配器的数据，以在应用界面上显示
     */
    private void initAssets() {
        try {
            // 打开资产目录下的mfg.txt文件，获取制造数据的输入流
            InputStream mfgJsonStream = getResources().getAssets().open("mfg.txt");
            // 将输入流转换为字符串形式的JSON数据
            String mfgJson = FreeApi_StringUtils.getString(mfgJsonStream);
            // 解析JSON数据，将其转换为实体对象列表，并进行数据过滤
            List<FreeUI_TabIndexGridEntity> mfgEntity = filteringData(new Gson().fromJson(mfgJson,
                    new TypeToken<List<FreeUI_TabIndexGridEntity>>() {
                    }.getType()));
            // 更新适配器数据，将制造实体数据转换为适当的格式，并传递给适配器
            mAdapter.setNewData(MyUtils.INSTANCE.setPicToDrawable(this,mfgEntity));
        } catch (IOException e) {
            // 打印异常信息，以便于调试和错误追踪
            e.printStackTrace();
        }
    }


/**
 * 过滤数据实体
 * 该方法用于根据特定条件过滤给定的实体列表
 * 主要功能是遍历实体列表，通过反射检查每个实体的跳转类是否有效
 * 目前代码中保留了一个未使用的过滤条件示例，可能需要在未来根据实际需求启用
 *
 * @param entity 实体列表，需要进行过滤的数据
 * @return 返回过滤后的实体列表
 */
private List<FreeUI_TabIndexGridEntity> filteringData(List<FreeUI_TabIndexGridEntity> entity){
    // 创建迭代器遍历实体列表
    Iterator<FreeUI_TabIndexGridEntity> it = entity.iterator();
    // 遍历实体列表
    while (it.hasNext()) {
        FreeUI_TabIndexGridEntity next = it.next();

        try {
            // 通过反射获取实体跳转类的名称
            Class<?> aClass = Class.forName(next.getSkipClass());
            String name = aClass.getSimpleName();
            // （示例代码）如果跳转类名称不在权限列表中，则移除该实体
            // 注意：此功能当前未启用，需要根据实际需求决定是否启用
//            if (!Constant.AUTHORITYLIST.contains(name)) {
//                it.remove()
//            }
        } catch (ClassNotFoundException e) {
            // 如果跳转类找不到，则打印错误信息
            e.printStackTrace();
        }

    }
    // 返回过滤后的实体列表
    return entity;
}


    private class MainAdapter extends BaseQuickAdapter<FreeUI_TabIndexGridEntity, DataBindBaseViewHolder> {

        public MainAdapter(@Nullable List<FreeUI_TabIndexGridEntity> data) {
            super(R.layout.item_main,data);
        }

        @Override
        protected void convert(DataBindBaseViewHolder helper, FreeUI_TabIndexGridEntity item) {
            ItemMainBinding binding = (ItemMainBinding) helper.getDataBinding();
            binding.setTabIndexGrid(item);
            binding.executePendingBindings();
        }
    }
}