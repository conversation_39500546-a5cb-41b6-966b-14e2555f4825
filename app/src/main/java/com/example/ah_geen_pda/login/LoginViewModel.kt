package com.example.ah_geen_pda.login

import android.Manifest
import android.app.Activity
import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.MutableLiveData
import com.example.ah_geen_pda.base.BaseViewModel
import com.example.ah_geen_pda.base.Constant
import com.example.ah_geen_pda.base.MyApp
import com.example.ah_geen_pda.entity.LoginEntity
import com.example.ah_geen_pda.model.LoginRepository
import com.example.ah_geen_pda.entity.ToastMessageBean
import com.google.gson.JsonObject
import com.google.gson.reflect.TypeToken
import com.lzy.okgo.model.Response
import com.pda.platform.ui.ui_pdaplatform.callback.FreeUI_EntityCallBack
import com.pda.platform.ui.ui_pdaplatform.dialog.FreeUI_DownloadFragmentDialog
import com.pda.platform.ui.ui_pdaplatform.entity.FreeUI_VersionEntity
import com.pda.platform.ui.ui_pdaplatform.utils_public.FreeApi_NetUtils
import com.pda.platform.ui.ui_pdaplatform.utils_public.FreeApi_SharePreferencesUtils
import com.pda.platform.ui.ui_pdaplatform.utils_public.FreeApi_Utils
import pub.devrel.easypermissions.EasyPermissions
import java.util.*

/**
 * FileName: LoginViewModel
 * Author: XJH
 * Date: 2021/5/12 18:43
 * Description:
 * History:
 */
class LoginViewModel : BaseViewModel(){

    var loginEntity = MutableLiveData<LoginEntity>()
    val PERMISSIONS_REQUEST_CODE = 10
    private var code = 0
    val loginRepository : LoginRepository = LoginRepository()

    fun initUser(context: AppCompatActivity){
        loginEntity.value = LoginEntity()
        Constant.REQUEST_URL_DEBUG = FreeApi_SharePreferencesUtils.getSharePre("FILE", "REQUEST_URL_DEBUG", Constant.REQUEST_URL_DEBUG, MyApp.app)
//        Constant.FILE_URL = FreeApi_SharePreferencesUtils.getSharePre("SUNNY_FILE", "FILE_URL_DEBUG", Constant.FILE_URL, MyApp.app)
        Constant.CURRENT_USER = FreeApi_SharePreferencesUtils.getSharePre("FILE", "CURRENT_USER","", MyApp.app)
        Constant.LANGUAGE = FreeApi_SharePreferencesUtils.getSharePre("FILE", "LANGUAGE","中文", MyApp.app)
        Constant.AUTHORITYLIST.clear()
        requestPermission(context)
//        detectionUpdate(context)

    }

    private fun requestPermission(context: Activity){
        //Manifest.permission.READ_PHONE_STATE 申请不到
        val perms = arrayOf(Manifest.permission.WRITE_EXTERNAL_STORAGE,
                Manifest.permission.CAMERA, Manifest.permission.READ_EXTERNAL_STORAGE)
        //权限判断，第一次弹出系统的授权提示框
        if (EasyPermissions.hasPermissions(context, *perms)) {
            //@AfterPermissionGranted 有权限直接执行 todo...
            //login();
        } else {
            //没有权限的话，先提示，点确定后弹出系统的授权提示框
            EasyPermissions.requestPermissions(context, "程序运行需要权限",
                    PERMISSIONS_REQUEST_CODE, *perms)
        }
    }

    private fun detectionUpdate(context: AppCompatActivity) {
        val manager: PackageManager = context.packageManager
        var info: PackageInfo? = null
        try {
            info = manager.getPackageInfo(context.packageName, 0)
        } catch (e: PackageManager.NameNotFoundException) {
            e.printStackTrace()
        }
        val version = info!!.versionName
        val callBack: FreeUI_EntityCallBack<FreeUI_VersionEntity> = object : FreeUI_EntityCallBack<FreeUI_VersionEntity>(object : TypeToken<FreeUI_VersionEntity?>() {}.type) {

            override fun onSuccess(response: Response<FreeUI_VersionEntity>) {
                val entity = response.body()
                entity.currentVersion = version
                val code = FreeApi_Utils.compareVersion(version, entity.version)
                if (code == -1) {
                    val downloadFragmentDialog = FreeUI_DownloadFragmentDialog()
                    downloadFragmentDialog.setData(entity)
                    downloadFragmentDialog.show(context.supportFragmentManager, "DOWNLOAD")
                }
            }
        }
        val map: Map<String, String> = HashMap()
        FreeApi_NetUtils.requestGetNet(Constant.FILE_URL, this, map, callBack)
    }

    fun login(userName:String,passWord:String){
        if (userName.isEmpty() || passWord.isEmpty()){
            defUI.toastEvent.value = ToastMessageBean("用户名或密码不能为空",false)
            return
        }

        val map = JsonObject()
        map.addProperty("ACTIONTYPE","LOGIN")
        map.addProperty("USERNAME",userName)
        map.addProperty("PASSWORD",passWord)
        launchOnlyResult({
            //登录请求
            val login = loginRepository.getLogin(map)
            Constant.CURRENT_USER = login.USER.USERNAME
            Constant.CURRENT_USER_NAME = login.USER.USERNAME
            defUI.toastEvent.value = ToastMessageBean("登录成功",true)
            defUI.startActivity.call()
        })
    }
}