package com.example.ah_geen_pda.login

import android.os.Bundle
import android.util.Log
import android.view.View
import android.view.inputmethod.EditorInfo
import com.example.ah_geen_pda.BR
import com.example.ah_geen_pda.MainActivity
import com.example.ah_geen_pda.R
import com.example.ah_geen_pda.base.BaseActivity
import com.example.ah_geen_pda.databinding.ActivityLoginBinding
import com.example.ah_geen_pda.entity.ToastMessageBean
import com.example.ah_geen_pda.setting.SettingActivity
import kotlinx.android.synthetic.main.activity_login.*
import pub.devrel.easypermissions.AppSettingsDialog
import pub.devrel.easypermissions.EasyPermissions

class LoginActivity : BaseActivity<LoginViewModel, ActivityLoginBinding>(), View.OnClickListener,EasyPermissions.PermissionCallbacks {
    override fun layoutId(): Int {
        return R.layout.activity_login
    }

    override fun initVariableId(): Int {
        return BR.loginViewModel
    }

    override fun initView(savedInstanceState: Bundle?) {
        viewModel.initUser(this)
        viewModel.defUI.startActivity.observe(this) {
            // 登录成功后，创建常驻通知
            com.example.ah_geen_pda.ui.urgentsystem.UrgentSystemActivity.createInitialNotification(this)
            // 启动主界面
            startActivity(MainActivity::class.java)
            finish()
        }

        // 设置回车键监听器
        etPassword.setOnEditorActionListener { v, actionId, event ->
            if (actionId == EditorInfo.IME_ACTION_DONE) {
                // 触发登录事件
                performLogin()
                return@setOnEditorActionListener true
            }
            false
        }
    }

    override fun initData() {
        btnLogin.setOnClickListener(this)
        ivSetting.setOnClickListener(this)
    }

    override fun onPermissionsGranted(requestCode: Int, perms: MutableList<String>?) {

    }

    override fun onPermissionsDenied(requestCode: Int, perms: MutableList<String>?) {
        viewModel.defUI.toastEvent.value = ToastMessageBean("用户授权失败", false)
        if (EasyPermissions.somePermissionPermanentlyDenied(this, perms!!)) {
            AppSettingsDialog.Builder(this).build().show()
        }
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<String?>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        EasyPermissions.onRequestPermissionsResult(requestCode, permissions, grantResults, this)
    }


    override fun onClick(v: View?) {
        when(v?.id){
            R.id.btnLogin ->{
                viewModel.login(etUser.text.toString(),etPassword.text.toString())
            }
            R.id.ivSetting ->{
                startActivity(SettingActivity::class.java)
            }
        }
    }

    private fun performLogin() {
        val username = etUser.text.toString()
        val password = etPassword.text.toString()

        Log.e("LoginActivity", "用户名: $username, 密码: $password")
        viewModel.login(username, password)
    }
}