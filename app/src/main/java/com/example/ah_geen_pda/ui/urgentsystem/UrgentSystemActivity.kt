package com.example.ah_geen_pda.ui.urgentsystem

import android.app.AlertDialog
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.View
import android.widget.*
import androidx.core.app.NotificationCompat
import androidx.databinding.BindingAdapter
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.OnLifecycleEvent
import androidx.lifecycle.ProcessLifecycleOwner
import com.example.ah_geen_pda.R
import com.example.ah_geen_pda.BR
import com.example.ah_geen_pda.base.BaseActivity
import com.example.ah_geen_pda.databinding.ActivityUrgentSystemBinding
import com.example.ah_geen_pda.entity.ToastMessageBean
import com.example.ah_geen_pda.utils.PopWindowsUtils
import com.example.ah_geen_pda.utils.VibrationAndMusicUtils
import androidx.lifecycle.Observer
import kotlinx.android.synthetic.main.activity_title.*
import kotlinx.android.synthetic.main.activity_urgent_system.*

class UrgentSystemActivity : BaseActivity<UrgentSystemViewModel, ActivityUrgentSystemBinding>(), LifecycleObserver {
    val siteList = ArrayList<String>()
    var mSitePop: PopWindowsUtils.GeneralPop? = null
    private var alert: AlertDialog? = null

    override fun layoutId(): Int {
        return R.layout.activity_urgent_system
    }

    override fun initVariableId(): Int {
        return BR.UrgentSystemViewModel
    }

    override fun initView(savedInstanceState: Bundle?) {
        // 设置标题文本
        tvTitle.text = "加急片数据资料查询"

        // 设置下拉栏默认值
        tvSite.text = "请选择站点"

        // 从 Intent 中获取站点信息
        intent.getStringExtra("site")?.let { site ->
            if (site != "请选择站点") {
                tvSite.text = site
                updateSite(site)
            }
        }

        // 创建通知渠道
        createNotificationChannel()

        // 创建初始常驻通知
        updatePersistentNotification("请选择站点", 0)

        // 设置按钮点击事件监听器
        btCheckData.setOnClickListener {
            val site = tvSite.text.toString()
            updateSite(site)
        }

        // 注册生命周期观察者
        ProcessLifecycleOwner.get().lifecycle.addObserver(this)

        viewModel.dataItemsCount.observeForever(dataItemsCountObserver)
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        // 处理通知点击时的新Intent
        intent?.getStringExtra("site")?.let { site ->
            if (site != "请选择站点" && site != tvSite.text.toString()) {
                tvSite.text = site
                updateSite(site)
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        // 移除生命周期观察者
        ProcessLifecycleOwner.get().lifecycle.removeObserver(this)
        // 移除 Observer
        viewModel.dataItemsCount.removeObserver(dataItemsCountObserver)
        // 关闭 AlertDialog
        alert?.dismiss()
    }

    override fun initData() {
        // 初始下拉栏信息
        siteList.add("化学站")
        siteList.add("键合站")
        siteList.add("黄光站")
        siteList.add("蒸镀站")
        siteList.add("研磨站")
        siteList.add("目检站")
        siteList.add("生管站")
        siteList.add("品管站")
        siteList.add("分选站")
        siteList.add("划裂站")
        siteList.add("测试站")
        siteList.add("薄膜站")
        siteList.add("FQC")
        siteList.add("抽测站")

        // 设置站点列表的点击监听器，用于弹出站点选择框
        flSiteList.setOnClickListener {
            if (mSitePop == null) {
                mSitePop = PopWindowsUtils.getOneChoosePop(this, flSiteList.width, siteList,
                    ivSiteArrow, object : PopWindowsUtils.PopTextAndPositionCallback {
                        override fun onSuccess(string: String, potion: Int) {
                            updateSite(string)
                        }
                    }
                )
            }

            if (mSitePop!!.isShowing) {
                mSitePop?.dismiss()
            } else {
                PopWindowsUtils.setArrowShow(ivSiteArrow)
                mSitePop?.showAsDropDown(flSiteList, 0, 0)
            }
        }

        // 默认打开页面后查看是否有震动和音乐，有则关闭
        VibrationAndMusicUtils.stopVibration()
        VibrationAndMusicUtils.stopMusic()
    }

    private fun startUrgentNotificationService() {
        val intent = Intent(this, UrgentNotificationService::class.java).apply {
            putExtra("site", tvSite.text.toString())
        }
        startService(intent)
    }

    private fun stopUrgentNotificationService() {
        val intent = Intent(this, UrgentNotificationService::class.java)
        stopService(intent)
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_STOP)
    fun onAppBackgrounded() {
        // 应用进入后台
        Log.e("Lifecycle", "应用进入后台")
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_START)
    fun onAppForegrounded() {
        // 应用进入前台
        Log.e("Lifecycle", "应用进入前台")
    }

    private val dataItemsCountObserver = Observer<Int> { count ->
        Log.e("监听数据项数量变化", "${count}")

        if (count > 0 && !isFinishing) {
            // 启动前台服务来播放提示音和震动
            startUrgentNotificationService()

            // 弹出确认弹窗提示
            val builder = AlertDialog.Builder(this)
            builder.setMessage("您共有${count}盒数据带做，请尽快安排作业")
            builder.setPositiveButton("确认") { dialog, _ ->
                // 只停止震动和音乐，不停止通知服务
                VibrationAndMusicUtils.stopVibration()
                VibrationAndMusicUtils.stopMusic()
                dialog.dismiss()
            }
            builder.setCancelable(false)
            alert = builder.create()

            // 使用 let 确保 alert 不为空再调用 show 方法
            alert?.let { it.show() }

            // 震动+音乐
            VibrationAndMusicUtils.startVibration(this)
            VibrationAndMusicUtils.startMusic(this, R.raw.urgent_film_prompt_sound2)
        }

        // 修改页面标题文本
        tvTitle.text = if (count > 0) {
            "您共有${count}盒数据带做，请尽快安排作业"
        } else {
            "加急片数据资料查询"
        }
        // 设置字体颜色为红色
        tvTitle.setTextColor(if (count > 0) resources.getColor(R.color.red) else resources.getColor(R.color.free_ui_glory_theme_color))

        // 更新通知栏信息
        val site = tvSite.text.toString()
//        不是"请选择站点"才更新
        if (site != "请选择站点") {
            updatePersistentNotification(site, count)
        }
    }

    companion object {
        private const val NOTIFICATION_CHANNEL_ID = "urgent_channel"
        private const val NOTIFICATION_ID = 1

        @JvmStatic
        fun createInitialNotification(context: Context) {
            // 创建通知渠道
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                val name = "Urgent Channel"
                val descriptionText = "Channel for urgent notifications"
                val importance = NotificationManager.IMPORTANCE_LOW
                val channel = NotificationChannel(NOTIFICATION_CHANNEL_ID, name, importance).apply {
                    description = descriptionText
                }
                val notificationManager: NotificationManager =
                    context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
                notificationManager.createNotificationChannel(channel)
            }

            // 创建通知 - 修复：使用单一任务模式，不清除任务栈
            val intent = Intent(context, UrgentSystemActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_SINGLE_TOP or Intent.FLAG_ACTIVITY_CLEAR_TOP
            }
            val pendingIntent: PendingIntent = PendingIntent.getActivity(
                context,
                0,
                intent,
                PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
            )

            val notification = NotificationCompat.Builder(context, NOTIFICATION_CHANNEL_ID)
                .setContentTitle("请选择站点")
                .setContentText("暂无加急数据")
                .setSmallIcon(R.drawable.log_tb)
                .setPriority(NotificationCompat.PRIORITY_LOW)
                .setContentIntent(pendingIntent)
                .setOngoing(true) // 设置为常驻通知
                .build()

            val notificationManager: NotificationManager =
                context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.notify(NOTIFICATION_ID, notification)
        }

        @JvmStatic
        fun startMonitoring(context: Context) {
            val intent = Intent(context, UrgentSystemActivity::class.java)
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
            context.startActivity(intent)
        }

        @JvmStatic
        @BindingAdapter("app:detainColor")
        fun setSomeAttribute(view: View, value: String) {
            if (value == "On") {
                view.setBackgroundColor(view.context.resources.getColor(R.color.free_ui_fire_red_theme_color_fade))
            } else {
                view.setBackgroundColor(view.context.resources.getColor(R.color.white))
            }
        }

        /**
         * 更新通知的方法
         *
         * 此方法用于创建和显示一个正在进行的通知，该通知包含标题、内容和站点信息
         * 它通过PendingIntent启动一个活动，以便用户点击通知时能够获取站点信息
         *
         * @param context 上下文，用于创建Intent和访问系统服务
         * @param title 通知的标题
         * @param content 通知的内容
         * @param site 通知中携带的站点信息
         */
        @JvmStatic
        fun updateNotification(context: Context, title: String, content: String, site: String) {
            // 创建一个Intent，用于启动活动 - 修复：不清除任务栈，保持Activity栈完整
            val intent = Intent(context, UrgentSystemActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_SINGLE_TOP or Intent.FLAG_ACTIVITY_CLEAR_TOP
                putExtra("site", site)  // 添加站点信息
            }

            // 创建一个PendingIntent，作为通知被点击时的响应
            val pendingIntent = PendingIntent.getActivity(
                context,
                0,
                intent,
                PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
            )

            // 构建通知对象，设置其标题、内容、图标、优先级、意图和持续状态
            val notification = NotificationCompat.Builder(context, NOTIFICATION_CHANNEL_ID)
                .setContentTitle(title)
                .setContentText(content)
                .setSmallIcon(R.drawable.log_tb)
                .setPriority(NotificationCompat.PRIORITY_LOW)
                .setContentIntent(pendingIntent)
                .setOngoing(true)
                .build()

            // 获取NotificationManager系统服务，用于发布通知
            val notificationManager: NotificationManager =
                context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            // 发布通知
            notificationManager.notify(NOTIFICATION_ID, notification)
        }
    }

    // 创建通知渠道
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val name = "Urgent Channel"
            val descriptionText = "Channel for urgent notifications"
            val importance = NotificationManager.IMPORTANCE_LOW
            val channel = NotificationChannel("urgent_channel", name, importance).apply {
                description = descriptionText
            }
            val notificationManager: NotificationManager =
                getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    // 修改更新通知的方法
    private fun updatePersistentNotification(site: String, count: Int) {
        val title = if (site == "请选择站点") "请选择站点" else "正在持续监控$site 站点"
        val contentText = if (count > 0) "您共有$count 盒数据带做，请尽快安排作业" else "暂无加急数据"
        
        // 只调用一次通知更新
        updateNotification(this, title, contentText, site)
    }

    private fun updateSite(site: String) {
        // 统一处理站点更新
        tvSite.text = site
        // 立即查询一次数据
        viewModel.fetchUrgentFilmInfo(site)
        // 更新定时服务的站点
        UrgentTimerService.startService(this, site)
        // 更新通知栏信息
        updatePersistentNotification(site, viewModel.dataItems.size)
    }
}
