package com.example.ah_geen_pda.ui.chipfrontsectionin

import android.os.Bundle
import android.view.KeyEvent
import android.view.inputmethod.EditorInfo
import android.widget.CompoundButton
import android.widget.TextView
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.customview.customView
import com.afollestad.materialdialogs.lifecycle.lifecycleOwner
import com.example.ah_geen_pda.R
import com.example.ah_geen_pda.BR
import com.example.ah_geen_pda.base.BaseActivity
import com.example.ah_geen_pda.databinding.ActivityChipfrontsectioninBinding
import com.example.ah_geen_pda.entity.ToastMessageBean
import kotlinx.android.synthetic.main.activity_chipfrontsectionin.*
import kotlinx.android.synthetic.main.activity_title.*
import kotlinx.android.synthetic.main.dialog_material_input.*

class ChipFrontSectionInAcitvity :BaseActivity<ChipFrontSectionInViewModel,ActivityChipfrontsectioninBinding>(){

    private var materialDialog: MaterialDialog? = null

    // 创建配件一和配件二输入框完整内容变量
    private var material1_all: String = ""
    private var material2_all: String = ""


    override fun layoutId(): Int {
        return R.layout.activity_chipfrontsectionin
    }

    override fun initVariableId(): Int {
        return BR.chipFrontSectionInViewModel
    }

    override fun initView(savedInstanceState: Bundle?) {
        tvTitle.text = "芯片前段CheckIn"

        initEdit(etLotID,object : EditTextListener{
            override fun onTextChangeListener(s: String) {
                viewModel.getLotInfo(s,false)
            }

        })

        initEdit(etLotCode,object : EditTextListener{
            override fun onTextChangeListener(s: String) {
                viewModel.getLotCodeInfo(s)
            }

        })

        btnTrackIn.setOnClickListener{
            viewModel.trackIn(etStepName.text.toString(),etPart.text.toString())
        }

        allCheck.setOnCheckedChangeListener { _, isChecked ->
            if (viewModel.lotItems.isEmpty()){
                viewModel.defUI.toastEvent.value = ToastMessageBean("请先添加批次数据",false)
                allCheck.isChecked = false
                return@setOnCheckedChangeListener
            }

            viewModel.checkChange(isChecked)
        }

        allTechnicalListCheck.setOnCheckedChangeListener { _, isChecked ->
            if (viewModel.componentItems.isEmpty()){
                viewModel.defUI.toastEvent.value = ToastMessageBean("请先添加批片数据",false)
                allTechnicalListCheck.isChecked = false
                return@setOnCheckedChangeListener
            }

            viewModel.checkTechnicalListChange(isChecked)
        }
    }

    override fun initData() {
        viewModel.defUI.startActivity.observe(this){finish()}

        initEdit(etDurable,object : EditTextListener{
            override fun onTextChangeListener(s: String) {
                viewModel.lotItems.forEach {
                    if (it.LOT.DURABLEID == s){
                        viewModel.defUI.toastEvent.value = ToastMessageBean("当前载具已添加",false)
                        return
                    }
                }
                viewModel.getLotInfo(s,true)
            }

        })

        // 观察配件输入对话框显示事件
        viewModel.showMaterialDialog.observe(this) { shouldShow ->
            if (shouldShow) {
                showMaterialInputDialog()
            }
        }
    }

    /**
     * 验证配件ID格式
     * 格式要求：前13个字符，以Y开头，中间包含-，例如Y100001-00002
     */
    private fun validateMaterialId(materialId: String): Boolean {
        if (materialId.isEmpty()) return true // 空值允许（配件2可选）

        // 取前13个字符
        val first13Chars = if (materialId.length >= 13) {
            materialId.substring(0, 13)
        } else {
            materialId
        }

        // 检查是否以Y开头且包含-
        return first13Chars.startsWith("Y") && first13Chars.contains("-")
    }

    /**
     * 处理配件输入框的回车事件
     */
    private fun handleMaterialEnterKey(editText: com.pda.platform.ui.ui_pdaplatform.view.FreeUI_ClearEditText) {
        val inputText = editText.text.toString().trim()



        if (inputText.isNotEmpty()) {
            // 默认取前13个字符
            var processedText = if (inputText.length >= 13) {
                inputText.substring(0, 13)
            } else {
                inputText
            }

            // 看一下是否有;号，有则根据分割符处理，取第一个
            if (inputText.contains(";")) {
               val splitText = inputText.split(";")
                if (splitText.isNotEmpty()) {
                    processedText = splitText[0]
                }
            }

            // 验证格式
            if (validateMaterialId(processedText)) {
                // 格式正确，更新输入框内容为处理后的值
                editText.setText(processedText)
                editText.setSelection(processedText.length)
                viewModel.defUI.toastEvent.value = ToastMessageBean("配件ID格式正确", true)
            } else {
                // 格式错误，显示错误提示
                viewModel.defUI.toastEvent.value = ToastMessageBean("配件ID格式错误，应以Y开头且包含-，如Y100001-00002", false)
                editText.selectAll()
            }
        }
    }

    /**
     * 显示配件信息输入对话框
     */
    private fun showMaterialInputDialog() {
        materialDialog = MaterialDialog(this)
            .cancelable(false)
            .cornerRadius(8f)
            .customView(R.layout.dialog_material_input, noVerticalPadding = false)
            .lifecycleOwner(this)
            .maxWidth(R.dimen.material_dialog_width)

        val customView = materialDialog!!.view
        val etMaterial1 = customView.findViewById<com.pda.platform.ui.ui_pdaplatform.view.FreeUI_ClearEditText>(R.id.etMaterial1)
        val etMaterial2 = customView.findViewById<com.pda.platform.ui.ui_pdaplatform.view.FreeUI_ClearEditText>(R.id.etMaterial2)
        val btnCancel = customView.findViewById<android.widget.Button>(R.id.btnCancel)
        val btnConfirm = customView.findViewById<android.widget.Button>(R.id.btnConfirm)

        // 为配件1输入框添加回车事件监听
        etMaterial1.setOnEditorActionListener { _, actionId, event ->
            if (actionId == EditorInfo.IME_ACTION_DONE ||
                (event != null && event.keyCode == KeyEvent.KEYCODE_ENTER && event.action == KeyEvent.ACTION_DOWN)) {
                // 保存配件1输入的完整内容
                material1_all = etMaterial1.text.toString().trim()
                handleMaterialEnterKey(etMaterial1)
                true
            } else {
                false
            }
        }

        // 为配件2输入框添加回车事件监听
        etMaterial2.setOnEditorActionListener { _, actionId, event ->
            if (actionId == EditorInfo.IME_ACTION_DONE ||
                (event != null && event.keyCode == KeyEvent.KEYCODE_ENTER && event.action == KeyEvent.ACTION_DOWN)) {
                 material2_all = etMaterial2.text.toString().trim()
                handleMaterialEnterKey(etMaterial2)
                true
            } else {
                false
            }
        }

        btnCancel.setOnClickListener {
            materialDialog?.dismiss()
            viewModel.showMaterialDialog.value = false
        }

        btnConfirm.setOnClickListener {
            val material1 = etMaterial1.text.toString().trim()
            val material2 = etMaterial2.text.toString().trim()

            if (material1.isEmpty()) {
                viewModel.defUI.toastEvent.value = ToastMessageBean("配件1不能为空", false)
                return@setOnClickListener
            }

            // 验证配件1格式
            if (!validateMaterialId(material1)) {
                viewModel.defUI.toastEvent.value = ToastMessageBean("配件1格式错误，应以Y开头且包含-，如Y100001-00002", false)
                return@setOnClickListener
            }

            // 验证配件2格式（如果不为空）
            if (material2.isNotEmpty() && !validateMaterialId(material2)) {
                viewModel.defUI.toastEvent.value = ToastMessageBean("配件2格式错误，应以Y开头且包含-，如Y100001-00002", false)
                return@setOnClickListener
            }

            // 查看配件1和配件2是否内容是否一致
            if (material1 == material2) {
                viewModel.defUI.toastEvent.value = ToastMessageBean("配件1和配件2不能一致", false)
                return@setOnClickListener
            }

            materialDialog?.dismiss()
            viewModel.showMaterialDialog.value = false
            viewModel.onMaterialInputConfirmed(material1, material2, material1_all, material2_all)
        }

        materialDialog?.show()
    }
}