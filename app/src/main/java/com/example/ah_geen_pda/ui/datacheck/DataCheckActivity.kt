package com.example.ah_geen_pda.ui.datacheck

import android.os.Bundle
import com.example.ah_geen_pda.R
import com.example.ah_geen_pda.BR
import com.example.ah_geen_pda.base.BaseActivity
import com.example.ah_geen_pda.databinding.ActivityDatacheckBinding
import com.example.ah_geen_pda.entity.ToastMessageBean
import kotlinx.android.synthetic.main.activity_datacheck.*
import kotlinx.android.synthetic.main.activity_title.*

class DataCheckActivity : BaseActivity<DataCheckViewModel, ActivityDatacheckBinding>(){
    override fun layoutId(): Int {
        return R.layout.activity_datacheck
    }

    override fun initVariableId(): Int {
        return BR.dataCheckViewModel
    }

    /**
     * 初始化视图并设置点击事件监听器
     * 此函数在活动或片段创建时调用，用于初始化用户界面
     * 它重置了显示文本，并在按钮点击时处理数据检查逻辑
     *
     * @param savedInstanceState 保存的实例状态，用于在界面重建时恢复先前的状态
     */
    override fun initView(savedInstanceState: Bundle?) {
        // 设置标题文本
        tvTitle.text = "资料核对"

        // 设置按钮点击事件监听器
        btCheckData.setOnClickListener {
            // 清空显示的文本信息
            tvLotID.text = ""
            tvPartName.text = ""
            tvDurableid.text = ""
            tvPARTNAME.text = ""
            tvStepdesc.text = ""
            tvMAINQTY.text = ""
            tvProcedureName.text = ""
            tvState.text = ""
            // 清空背景资源
            ivRecipeName.setBackgroundResource(0)

            // 检查批号输入框是否为空
            if (etLot.text.toString() == ""){
                // 如果为空，显示提示信息并返回
                viewModel.defUI.toastEvent.value = ToastMessageBean("请先扫描批号",false)
                return@setOnClickListener
            }

            // 调用视图模型获取批号信息
            viewModel.getLotInfo(etLot.text.toString(),false)
        }

    }

    override fun initData() {

        initEdit(etDurable,object : EditTextListener{
            override fun onTextChangeListener(s: String) {
                viewModel.dataItems.forEach {
                    tvLotID.text = ""
                    tvPartName.text = ""
                    tvDurableid.text = ""
                    tvPARTNAME.text = ""
                    tvStepdesc.text = ""
                    tvMAINQTY.text = ""
                    tvProcedureName.text = ""
                    tvState.text = ""
                    ivRecipeName.setBackgroundResource(0)
                    if (it.LOT.DURABLEID == s){
                        viewModel.defUI.toastEvent.value = ToastMessageBean("当前载具已添加",false)
                        return
                    }
                }
                viewModel.getLotInfo(s,true)
            }

        })

        viewModel.defUI.callObserve.observe(this){
            mBinding.setVariable(BR.dataCheckViewModel,viewModel)
        }
    }
}