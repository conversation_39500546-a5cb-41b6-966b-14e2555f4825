package com.example.ah_geen_pda.ui.inventory

import androidx.databinding.ObservableArrayList
import com.example.ah_geen_pda.R
import com.example.ah_geen_pda.base.BaseViewModel
import com.example.ah_geen_pda.entity.LoginEntity
import com.example.ah_geen_pda.entity.LotInventoryEntity
import com.example.ah_geen_pda.model.InventoryRepository
import com.example.ah_geen_pda.model.LotInfoRepository
import com.glorysoft.sunnypda.entity.ui.CallObserveBean
import com.google.gson.JsonObject
import me.tatarka.bindingcollectionadapter2.BR
import me.tatarka.bindingcollectionadapter2.ItemBinding

class InventoryViewModel :BaseViewModel() {
    var dataItems = ObservableArrayList<Any>()
    var lotEntity = LotInventoryEntity()
    val inventoryRepository : InventoryRepository = InventoryRepository()
    var dataBinding =
        ItemBinding.of<Any>(BR.dataItem, R.layout.item_data_check_list)

    fun getLotInfo(map:JsonObject){
        launchOnlyResult({
            lotEntity = inventoryRepository.getLotInventory(map)
            defUI.callObserve.value = CallObserveBean(0,lotEntity)
        })
    }

}