package com.example.ah_geen_pda.ui.countdown

import android.app.AlertDialog
import android.os.Bundle
import android.util.Log
import androidx.lifecycle.Observer
import com.example.ah_geen_pda.BR
import com.example.ah_geen_pda.R
import com.example.ah_geen_pda.base.BaseActivity
import com.example.ah_geen_pda.databinding.ActivityCountdownBinding
import com.example.ah_geen_pda.entity.ToastMessageBean
import com.example.ah_geen_pda.utils.VibrationAndMusicUtils
import kotlinx.android.synthetic.main.activity_countdown.*
import kotlinx.android.synthetic.main.activity_title.*

/**
 * 倒计时页面Activity
 * 实现倒计时功能，包括载具输入、批次管理、倒计时显示等功能
 */
class CountdownActivity : BaseActivity<CountdownViewModel, ActivityCountdownBinding>() {

    companion object {
        private const val TAG = "CountdownActivity"
    }

    private var firstStageDialog: AlertDialog? = null
    private var secondStageDialog: AlertDialog? = null

    override fun layoutId(): Int {
        Log.d(TAG, "设置布局ID")
        return R.layout.activity_countdown
    }

    override fun initVariableId(): Int {
        Log.d(TAG, "设置ViewModel变量ID")
        return BR.countdownViewModel
    }

    override fun initView(savedInstanceState: Bundle?) {
        Log.d(TAG, "初始化视图")
        
        // 设置标题
        tvTitle.text = "计时器"

        // 初始化载具输入框
        initEdit(etDurable, object : EditTextListener {
            override fun onTextChangeListener(s: String) {
                Log.d(TAG, "载具输入框内容变化: $s")
                if (s.isNotBlank()) {
                    viewModel.getLotInfo(s)
                    etDurable.setText("") // 清空输入框
                }
            }
        })

        // 开始按钮点击事件
        btnStart.setOnClickListener {
            Log.d(TAG, "开始按钮被点击")
            viewModel.startCountdown()
        }

        // 初始化环形进度条 - 增强版
        circularProgressView.apply {
            setMaxProgress(100)
            setProgress(0)
            setCountdownText("0")
            setStrokeWidth(25f)
            setTextSize(120f)
            // 初始化段数显示（默认显示1/1，等待获取实际数据）
            setStageInfo(1, 1)
            Log.d(TAG, "环形进度条初始化完成 - 增强版")
        }
        
        Log.d(TAG, "视图初始化完成")
    }

    override fun initData() {
        Log.d(TAG, "初始化数据观察者")
        
        // 观察页面跳转事件
        viewModel.defUI.startActivity.observe(this) { 
            Log.d(TAG, "接收到页面跳转事件，关闭当前页面")
            finish() 
        }

        // 观察倒计时文本变化
        viewModel.countdownText.observe(this, Observer { text ->
            Log.v(TAG, "倒计时文本更新: $text")
            circularProgressView.setCountdownText(text)
        })

        // 观察倒计时状态变化
        viewModel.countdownStatus.observe(this, Observer { status ->
            Log.d(TAG, "倒计时状态更新: $status")
            tvCountdownStatus.text = status
        })

        // 观察按钮文本变化
        viewModel.buttonText.observe(this, Observer { text ->
            Log.d(TAG, "按钮文本更新: $text")
            btnStart.text = text
        })

        // 观察批次数量变化
        viewModel.lotCount.observe(this, Observer { count ->
            Log.d(TAG, "批次数量更新: $count")
            tvLotCount.text = count
        })

        // 观察环形进度条更新
        viewModel.updateCircularProgress.observe(this, Observer { (currentProgress, maxProgress, remainingTime) ->
            Log.v(TAG, "更新环形进度条: 当前进度=$currentProgress, 最大进度=$maxProgress, 剩余时间=$remainingTime")
            
            circularProgressView.apply {
                setMaxProgress(maxProgress)
                setProgress(currentProgress)
                updateProgressColor(remainingTime, maxProgress)
            }
        })

        // 观察第一段完成对话框显示事件
        viewModel.showFirstStageCompleteDialog.observe(this, Observer { shouldShow ->
            if (shouldShow) {
                Log.d(TAG, "显示第一段完成对话框")
                showFirstStageCompleteDialog()
            }
        })

        // 观察第二段完成对话框显示事件
        viewModel.showSecondStageCompleteDialog.observe(this, Observer { shouldShow ->
            if (shouldShow) {
                Log.d(TAG, "显示第二段完成对话框")
                showSecondStageCompleteDialog()
            }
        })

        // 观察段数信息更新
        viewModel.updateStageInfo.observe(this, Observer { (currentStage, totalStages) ->
            Log.d(TAG, "段数信息更新: 当前段数=$currentStage, 总段数=$totalStages")
            circularProgressView.setStageInfo(currentStage, totalStages)

            // 根据段数输出详细信息
            when (totalStages) {
                1 -> Log.i(TAG, "【倒计时段数】检测到单段倒计时模式")
                2 -> Log.i(TAG, "【倒计时段数】检测到双段倒计时模式，当前处于第${currentStage}段")
                else -> Log.i(TAG, "【倒计时段数】检测到多段倒计时模式，总共${totalStages}段，当前第${currentStage}段")
            }
        })

        Log.d(TAG, "数据观察者初始化完成 - 包含段数信息观察")
    }

    /**
     * 显示第一段倒计时完成对话框
     */
    private fun showFirstStageCompleteDialog() {
        Log.d(TAG, "创建并显示第一段完成对话框")
        
        // 先停止之前的震动和音乐
        VibrationAndMusicUtils.stopVibration()
        VibrationAndMusicUtils.stopMusic()
        
        // 启动震动和音乐
        VibrationAndMusicUtils.startVibration(this)
        VibrationAndMusicUtils.startMusic(this, R.raw.urgent_film_prompt_sound2)

        val builder = AlertDialog.Builder(this)
        builder.setTitle("第一段倒计时完成")
        builder.setMessage("第一段倒计时已完成，是否继续第二段倒计时？")
        builder.setPositiveButton("继续") { dialog, _ ->
            Log.d(TAG, "用户选择继续第二段倒计时")
            
            // 停止震动和音乐
            VibrationAndMusicUtils.stopVibration()
            VibrationAndMusicUtils.stopMusic()
            
            dialog.dismiss()
            // 这里不需要调用startCountdown，因为按钮文本已经变为"继续"
            // 继续倒计时
            viewModel.startCountdown()

        }
        builder.setNegativeButton("取消") { dialog, _ ->
            Log.d(TAG, "用户取消继续倒计时")
            
            // 停止震动和音乐
            VibrationAndMusicUtils.stopVibration()
            VibrationAndMusicUtils.stopMusic()
            
            dialog.dismiss()
            // 重置状态
            viewModel.executeTrackOut()
        }
        builder.setCancelable(false)
        
        firstStageDialog = builder.create()
        firstStageDialog?.show()
        
        Log.d(TAG, "第一段完成对话框已显示")
    }

    /**
     * 显示第二段倒计时完成对话框
     */
    private fun showSecondStageCompleteDialog() {
        Log.d(TAG, "创建并显示第二段完成对话框")
        
        // 先停止之前的震动和音乐
        VibrationAndMusicUtils.stopVibration()
        VibrationAndMusicUtils.stopMusic()
        
        // 启动震动和音乐
        VibrationAndMusicUtils.startVibration(this)
        VibrationAndMusicUtils.startMusic(this, R.raw.urgent_film_prompt_sound2)

        val builder = AlertDialog.Builder(this)
        builder.setTitle("倒计时完成")
        builder.setMessage("所有倒计时已完成，是否执行出站操作？")
        builder.setPositiveButton("确认出站") { dialog, _ ->
            Log.d(TAG, "用户确认执行出站操作")
            
            // 停止震动和音乐
            VibrationAndMusicUtils.stopVibration()
            VibrationAndMusicUtils.stopMusic()
            
            dialog.dismiss()
            viewModel.executeTrackOut()

            // 清空批次列表和计时器
            resetCountdownState()
        }
        builder.setNegativeButton("取消") { dialog, _ ->
            Log.d(TAG, "用户取消出站操作")
            
            // 停止震动和音乐
            VibrationAndMusicUtils.stopVibration()
            VibrationAndMusicUtils.stopMusic()
            
            dialog.dismiss()
            // 清空批次列表和计时器
            resetCountdownState()
        }
        builder.setCancelable(false)
        
        secondStageDialog = builder.create()
        secondStageDialog?.show()
        
        Log.d(TAG, "第二段完成对话框已显示")
    }

    override fun onDestroy() {
        Log.d(TAG, "Activity销毁，清理资源")
        
        // 停止震动和音乐
        VibrationAndMusicUtils.stopVibration()
        VibrationAndMusicUtils.stopMusic()
        
        // 关闭对话框
        firstStageDialog?.dismiss()
        secondStageDialog?.dismiss()
        
        super.onDestroy()
    }

    override fun onPause() {
        super.onPause()
        Log.d(TAG, "Activity暂停")
        
        // 暂停时停止震动和音乐
        VibrationAndMusicUtils.stopVibration()
        VibrationAndMusicUtils.stopMusic()
    }

    private fun resetCountdownState(){
//        清空批次列表信息
        viewModel.lotItems.clear()

//        清空计时器 信息
        viewModel.countdownText.value = "0"
        viewModel.countdownStatus.value = "准备开始"
        viewModel.buttonText.value = "开始倒计时"
        viewModel.lotCount.value = "(0)"
    }
}
