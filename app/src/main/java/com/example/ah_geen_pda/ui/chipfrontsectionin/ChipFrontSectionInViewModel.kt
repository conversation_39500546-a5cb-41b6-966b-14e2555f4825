package com.example.ah_geen_pda.ui.chipfrontsectionin

import androidx.databinding.ObservableArrayList
import androidx.lifecycle.MutableLiveData
import com.example.ah_geen_pda.R
import com.example.ah_geen_pda.base.BaseViewModel
import com.example.ah_geen_pda.entity.*
import com.example.ah_geen_pda.model.LotInfoRepository
import com.google.gson.Gson
import com.google.gson.JsonObject
import com.google.gson.JsonParser
import me.tatarka.bindingcollectionadapter2.BR
import me.tatarka.bindingcollectionadapter2.ItemBinding

class ChipFrontSectionInViewModel : BaseViewModel() {
    var lotItems = ObservableArrayList<LotEntity>()
    var componentItems = ObservableArrayList<ComponentList>()

    var lotItemBinding =
        ItemBinding.of<LotEntity>(BR.trackInItem, R.layout.item_track_in)
    val lotRepository: LotInfoRepository = LotInfoRepository()
    var lotDataBinding =
        ItemBinding.of<ComponentList>(BR.dataItem, R.layout.item_lot_list)

    // 配件信息输入对话框相关
    val showMaterialDialog = MutableLiveData<Boolean>()
    val materialInputResult = MutableLiveData<String>()

    // 临时保存trackIn参数
    private var tempEqpID = ""
    private var tempCarrierId = ""
    private var tempLotList: ArrayList<LotMessage> = arrayListOf()
    private var tempLotCodeList: ArrayList<ComponentList> = arrayListOf()
    fun getLotInfo(lotid: String,b:Boolean) {
        val map = JsonObject()
        if (b){
            map.addProperty("ACTIONTYPE","LOTINFOBYDURABLE")
            map.addProperty("DURABLE",lotid)
        }else{
            map.addProperty("ACTIONTYPE","LOTINFOBYID")
            map.addProperty("LOTID",lotid)
        }
        launchOnlyResult({
            val lotEntity = lotRepository.getLotInfoTo(map)
            if (lotEntity.LOT.STATE != "WAIT"){
                defUI.toastEvent.value =
                    ToastMessageBean("当前批次状态为"+lotEntity.LOT.STATE+",无法进站",false)
                return@launchOnlyResult
            }

            componentItems.addAll(lotEntity.LOT.COMPONENTLIST)
            lotEntity.LOT.start = lotEntity.LOT.COMPONENTLIST[0].ATTRIBUTE1
            lotEntity.LOT.end = lotEntity.LOT.COMPONENTLIST[lotEntity.LOT.COMPONENTLIST.size-1].ATTRIBUTE1
            lotItems.add(lotEntity)
        })
    }

    fun getLotCodeInfo(lotid: String) {
        val map = JsonObject()
        map.addProperty("ACTIONTYPE","LOTINFOBYID")
        map.addProperty("LOTID",lotid)

        launchOnlyResult({
            val lotEntity = lotRepository.getLotInfo(map)
            lotEntity.LOT.COMPONENTLIST.forEach{
                componentItems.add(it)
            }

        })
    }

    fun checkChange(ischeck : Boolean){
        lotItems.forEach {
            it.LOT.showCheck = ischeck
        }
    }

    fun checkTechnicalListChange(ischeck : Boolean){
        componentItems.forEach {
            it.mShowCheck = ischeck
        }
    }

    fun trackIn(eqpID:String,carrierId:String) {
        var lotList : ArrayList<LotMessage> = arrayListOf()
        var lotCodeList : ArrayList<ComponentList> = arrayListOf()
        var material = ""; // 配件ID
        var needMaterialInput = false
        var materialLotId = ""

        lotItems.forEach {
            if (it.LOT.STATE != "WAIT"){
                defUI.toastEvent.value =
                    ToastMessageBean("批次"+it.LOT.LOTID+"状态为"+it.LOT.STATE+",无法进站",false)
                return
            }
            if (it.LOT.showCheck){
                val lotMessage = LotMessage(it.LOT.LOTID)
                lotList.add(lotMessage)
            }

            // 查看批次是否需要输入配件信息
            if (it.MATERIAL == "Y"){
                needMaterialInput = true
                materialLotId = it.LOT.LOTID
            }
        }

        // 如果需要输入配件信息，弹出对话框
        if (needMaterialInput) {
            showMaterialDialog.value = true
            // 保存参数，等待用户输入配件信息后再继续执行
            tempEqpID = eqpID
            tempCarrierId = carrierId
            tempLotList = lotList
            tempLotCodeList = lotCodeList
            return
        }

        // 继续执行进站逻辑
        performTrackIn(eqpID, carrierId, lotList, lotCodeList, material, "")
    }

    /**
     * 执行进站操作
     */
    private fun performTrackIn(eqpID: String, carrierId: String, lotList: ArrayList<LotMessage>, lotCodeList: ArrayList<ComponentList>, material: String, material_all: String) {
        if (lotList.isEmpty()){
            defUI.toastEvent.value = ToastMessageBean("未发现选中批次", false)
            return
        }

        val finalLotCodeList = ArrayList<ComponentList>()
        componentItems.forEach {
            if (it.mShowCheck){
                finalLotCodeList.add(it)
            }
        }

        val map = JsonObject()
        map.addProperty("ACTIONTYPE","DefaultTrackIn")
        map.addProperty("EQUIPMENTID",eqpID)
        map.addProperty("CARRIERID",carrierId)
        map.addProperty("MATERIAL",material)
        map.addProperty("MATERIAL_ALL",material_all)
        map.addProperty("ISCHECKSTEPAUTHORITY",true)
        map.add("LOTLIST", JsonParser().parse(Gson().toJson(lotList)).asJsonArray)
        map.add("COMPONENTLIST", JsonParser().parse(Gson().toJson(finalLotCodeList)).asJsonArray)
        launchOnlyResult({
            lotRepository.defaultTrackInTo(map)
            defUI.toastEvent.value = ToastMessageBean("进站成功", true)
            defUI.startActivity.call()
        })
    }

    /**
     * 处理配件输入结果
     */
    fun onMaterialInputConfirmed(material1: String, material2: String, material1_all: String, material2_all: String) {


        // 取字符串前前13个字符
        val material1_ItemNumber = if (material1.isNotEmpty()) material1.substring(0, 13) else ""
        val material2_ItemNumber = if (material2.isNotEmpty()) material2.substring(0, 13) else ""


        val material = if (material1_ItemNumber.isNotEmpty() && material2_ItemNumber.isNotEmpty()) {
            "$material1_ItemNumber;$material2_ItemNumber"
        } else if (material1_ItemNumber.isNotEmpty()) {
            material1_ItemNumber
        } else if (material2_ItemNumber.isNotEmpty()){
            material2_ItemNumber
        }else {
            ""
        }
        materialInputResult.value = material


        val material_all = if (material1_all.isNotEmpty() && material2_all.isNotEmpty()) {
            "$material1_all||$material2_all"
        } else if (material1_all.isNotEmpty()) {
            material1_all
        } else if (material2_all.isNotEmpty()){
            material2_all
        }else {
            ""
        }



        // 继续执行之前中断的进站操作
        performTrackIn(tempEqpID, tempCarrierId, tempLotList, tempLotCodeList, material, material_all)
    }


}