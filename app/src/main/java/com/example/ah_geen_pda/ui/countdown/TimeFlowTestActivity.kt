package com.example.ah_geen_pda.ui.countdown

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import androidx.appcompat.app.AppCompatActivity
import com.example.ah_geen_pda.R

/**
 * 时间流逝感倒计时控件测试Activity
 * 用于测试新设计的时间流逝视觉效果
 */
class TimeFlowTestActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "TimeFlowTestActivity"
        private const val TOTAL_TIME = 60 // 总时间60秒
        private const val UPDATE_INTERVAL = 100L // 更新间隔100ms
    }

    private lateinit var circularProgressView: CircularProgressView
    private val handler = Handler(Looper.getMainLooper())
    private var currentTime = TOTAL_TIME
    private var isRunning = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_countdown)

        Log.d(TAG, "时间流逝感测试Activity启动")

        initViews()
        startCountdownTest()
    }

    /**
     * 初始化视图
     */
    private fun initViews() {
        Log.d(TAG, "初始化视图组件")

        circularProgressView = findViewById(R.id.circular_progress_view)
        
        // 设置初始参数
        circularProgressView.apply {
            setMaxProgress(TOTAL_TIME)
            setProgress(TOTAL_TIME)
            setStageInfo(1, 3) // 假设有3个阶段
            setCountdownText(formatTime(TOTAL_TIME))
        }

        Log.d(TAG, "视图初始化完成")
    }

    /**
     * 开始倒计时测试
     */
    private fun startCountdownTest() {
        Log.d(TAG, "开始时间流逝感倒计时测试")
        
        isRunning = true
        updateCountdown()
    }

    /**
     * 更新倒计时
     */
    private fun updateCountdown() {
        if (!isRunning || currentTime <= 0) {
            Log.d(TAG, "倒计时结束")
            return
        }

        // 更新时间显示
        circularProgressView.setCountdownText(formatTime(currentTime))
        circularProgressView.setProgress(currentTime)
        circularProgressView.updateProgressColor(currentTime, TOTAL_TIME)

        // 根据时间阶段更新段数
        val stage = when {
            currentTime > TOTAL_TIME * 2 / 3 -> 1
            currentTime > TOTAL_TIME / 3 -> 2
            else -> 3
        }
        circularProgressView.setStageInfo(stage, 3)

        Log.v(TAG, "更新倒计时: 当前时间=$currentTime, 阶段=$stage")

        // 减少时间（模拟每100ms减少0.1秒）
        currentTime -= 0.1f

        // 安排下次更新
        handler.postDelayed({ updateCountdown() }, UPDATE_INTERVAL)
    }

    /**
     * 格式化时间显示
     */
    private fun formatTime(timeInSeconds: Float): String {
        val seconds = timeInSeconds.toInt()
        val minutes = seconds / 60
        val remainingSeconds = seconds % 60
        return String.format("%02d:%02d", minutes, remainingSeconds)
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "Activity销毁，停止倒计时")
        
        isRunning = false
        handler.removeCallbacksAndMessages(null)
        
        // 清理控件资源
        if (::circularProgressView.isInitialized) {
            circularProgressView.cleanup()
        }
    }
}
