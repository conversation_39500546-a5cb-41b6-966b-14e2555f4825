package com.example.ah_geen_pda.ui.urgentsystem

import android.app.*
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.IBinder
import androidx.core.app.NotificationCompat
import com.example.ah_geen_pda.MainActivity
import com.example.ah_geen_pda.R
import com.example.ah_geen_pda.utils.VibrationAndMusicUtils

class UrgentNotificationService : Service() {

    private val CHANNEL_ID = "urgent_channel"
    private val NOTIFICATION_ID = 1


    private lateinit var intent: Intent
    private lateinit var pendingIntent: PendingIntent
    private var currentSite: String = "请选择站点"

    override fun onCreate() {
        super.onCreate()

        // 初始化 Intent 和 PendingIntent
        intent = Intent(this, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }
        pendingIntent = PendingIntent.getActivity(this, 0, intent, PendingIntent.FLAG_IMMUTABLE)

        createNotificationChannel()
        val packageName = this.packageName
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        // 保存传入的站点信息
        currentSite = intent?.getStringExtra("site") ?: "请选择站点"
        
        // 只播放音乐和震动
        VibrationAndMusicUtils.startVibration(this)
        VibrationAndMusicUtils.startMusic(this, R.raw.urgent_film_prompt_sound2)
        return START_NOT_STICKY
    }

    override fun onDestroy() {
        super.onDestroy()
        VibrationAndMusicUtils.stopVibration()
        VibrationAndMusicUtils.stopMusic()
    }

    override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    /**
     * 创建用于紧急通知的通知渠道
     *
     * 在Android Oreo及以上版本中，通知必须与通知渠道关联，以提供更细致的通知管理功能
     * 此函数负责创建一个高优先级的紧急通知渠道，以便应用可以发送紧急类型的通知
     */
    private fun createNotificationChannel() {
        // 检查设备是否运行在Android Oreo或更高版本
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            // 定义通知渠道的名称、描述和重要性
            val name = "Urgent Channel"
            val descriptionText = "Channel for urgent notifications"
            val importance = NotificationManager.IMPORTANCE_HIGH

            // 创建通知渠道实例并设置描述
            val channel = NotificationChannel(CHANNEL_ID, name, importance).apply {
                description = descriptionText
            }

            // 获取系统的通知管理服务
            val notificationManager: NotificationManager =
                getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

            // 在通知管理器中创建通知渠道
            notificationManager.createNotificationChannel(channel)
        }
    }

    private fun createNotification(): Notification {
        // 修复：直接创建PendingIntent，不使用TaskStackBuilder避免清除任务栈
        val intent = Intent(this, UrgentSystemActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_SINGLE_TOP or Intent.FLAG_ACTIVITY_CLEAR_TOP
            putExtra("site", currentSite)
        }

        // 获取 PendingIntent
        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            intent,
            PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
        )

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("加急片数据资料查询")
            .setContentText("您共有数据带做，请尽快安排作业")
            .setSmallIcon(R.drawable.log_tb)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setContentIntent(pendingIntent)
            .build()
    }

    companion object {
        private var currentSite: String = "请选择站点"
    }
}
