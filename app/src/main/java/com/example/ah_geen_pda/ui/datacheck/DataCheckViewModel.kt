package com.example.ah_geen_pda.ui.datacheck

import androidx.databinding.ObservableArrayList
import com.example.ah_geen_pda.R
import com.example.ah_geen_pda.base.BaseViewModel
import com.example.ah_geen_pda.entity.ComponentList
import com.example.ah_geen_pda.entity.LotEntity
import com.example.ah_geen_pda.entity.ToastMessageBean
import com.example.ah_geen_pda.model.LotInfoRepository
import com.glorysoft.sunnypda.entity.ui.CallObserveBean
import com.google.gson.JsonObject
import me.tatarka.bindingcollectionadapter2.BR
import me.tatarka.bindingcollectionadapter2.ItemBinding

class DataCheckViewModel: BaseViewModel() {

    var lotEntity = LotEntity()
    val loginRepository : LotInfoRepository = LotInfoRepository()
    var dataItems = ObservableArrayList<LotEntity>()
    var componentItems = ObservableArrayList<ComponentList>()
    var lotDataBinding =
        ItemBinding.of<ComponentList>(BR.dataItem, R.layout.item_data_check_list)
    var durableDataBinding =
        ItemBinding.of<LotEntity>(BR.dataItem, R.layout.item_durabledata_check_list)

    /**
     * 获取车位信息
     *
     * 此函数根据提供的车位ID和一个布尔标志来决定查询车位信息的方式如果布尔标志为true，则通过耐用品ID查询车位信息；
     * 否则，通过车位ID查询此函数首先创建一个JSON对象来准备请求参数，然后根据布尔标志添加相应的参数和动作类型，
     * 最后调用后台服务获取车位信息，并处理和更新相关数据项
     *
     * @param lotid 车位ID，用于查询车位信息
     * @param b 布尔值，决定查询车位信息的方式（通过耐用品ID或车位ID）
     */
    fun getLotInfo(lotid:String,b:Boolean){
        // 创建一个JSON对象来存储请求参数
        val map = JsonObject()
        // 根据布尔标志决定添加的参数和动作类型
        if (b){
            map.addProperty("ACTIONTYPE","LOTINFOBYDURABLE")
            map.addProperty("DURABLE",lotid)
        }else{
            map.addProperty("ACTIONTYPE","LOTINFOBYID")
            map.addProperty("LOTID",lotid)
        }
        // 启动一个协程来执行异步任务，并处理结果
        launchOnlyResult({
            // 清空数据项和组件项列表以准备接收新的数据
            dataItems.clear()
            componentItems.clear()
            // 通过调用登录仓库的getLotInfo方法获取车位信息
            lotEntity = loginRepository.getLotInfo(map)
            // 为每个组件设置其位置编号
            for (i in 0..lotEntity.LOT.COMPONENTLIST.size-1) {
                lotEntity.LOT.COMPONENTLIST[i].post = i+1
            }
            // 如果组件项列表不包含所有新的组件项，则添加它们，并更新起始和结束属性
            if (!componentItems.containsAll(lotEntity.LOT.COMPONENTLIST)){
                componentItems.addAll(lotEntity.LOT.COMPONENTLIST)
                lotEntity.LOT.start = componentItems[0].ATTRIBUTE1
                lotEntity.LOT.end = componentItems[componentItems.size-1].ATTRIBUTE1
                // 将更新后的车位实体添加到数据项列表中
                dataItems.add(lotEntity)
            }
            // 通知观察者，车位信息已更新
            defUI.callObserve.value = CallObserveBean(0,lotEntity)
        })
    }
}