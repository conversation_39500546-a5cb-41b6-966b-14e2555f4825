package com.example.ah_geen_pda.ui.inventory

import android.os.Bundle
import android.view.View
import com.example.ah_geen_pda.BR
import com.example.ah_geen_pda.R
import com.example.ah_geen_pda.base.BaseActivity
import com.example.ah_geen_pda.databinding.ActivityInventoryBinding
import com.example.ah_geen_pda.entity.LotInventoryEntity
import com.example.ah_geen_pda.utils.PopWindowsUtils
import com.google.gson.JsonObject
import com.pda.platform.ui.ui_pdaplatform.datepicker.CustomDatePicker
import com.pda.platform.ui.ui_pdaplatform.utils_public.FreeApi_DateUtils
import kotlinx.android.synthetic.main.activity_inventory.*
import kotlinx.android.synthetic.main.activity_title.*
import java.util.*
import kotlin.collections.ArrayList

class InventoryActivity : BaseActivity<InventoryViewModel, ActivityInventoryBinding>(),
    View.OnClickListener {

    var mSitePop: PopWindowsUtils.GeneralPop? = null
    var mDatePicker: CustomDatePicker? = null
    var beginDate: String? = null
    val siteList = ArrayList<String>()

    override fun layoutId(): Int {
        return R.layout.activity_inventory
    }

    override fun initVariableId(): Int {
        return BR.inventoryViewModel
    }

    override fun initView(savedInstanceState: Bundle?) {
        tvTitle.text = "盘点"
        flTimeList.setOnClickListener(this)
        beginDate =
            FreeApi_DateUtils.getAfterStringDate(Date(System.currentTimeMillis()), "yyyy-MM-dd", -0)
        tvTime.text = beginDate
        initDatePicker()
    }

    /**
     * 初始化数据，包括站点列表和文本监听器
     */
    override fun initData() {
        // 添加各个站点到站点列表
        siteList.add("键合站");
        siteList.add("黄光站");
        siteList.add("蒸镀站");
        siteList.add("研磨站");
        siteList.add("目检站");
        siteList.add("生管站");
        siteList.add("品管站");
        siteList.add("分选站");
        siteList.add("划裂站");
        siteList.add("测试站");
        siteList.add("薄膜站");
        siteList.add("FQC");
        siteList.add("化学站");
        siteList.add("抽测站");
        siteList.add("封装站");
        siteList.add("封测站");

        // 初始化wafer ID文本框的监听器，用于获取lot信息
        initEdit(etWaferID,object : EditTextListener{
            override fun onTextChangeListener(s: String) {
                val map = JsonObject()
                map.addProperty("ACTIONTYPE","LOTINFOBYID")
                map.addProperty("LOTID",s)
                map.addProperty("LOTALIAS",tvTime.text.toString())
                map.addProperty("EQUIPMENTID",tvSite.text.toString())
                viewModel.getLotInfo(map)
                etWaferID.setText("")
            }
        })
        // 初始化耐用ID文本框的监听器，用于获取lot信息
        initEdit(etDurableID,object : EditTextListener{
            override fun onTextChangeListener(s: String) {
                val map = JsonObject()
                map.addProperty("ACTIONTYPE","LOTINFOBYDURABLE")
                map.addProperty("DURABLE",s)
                map.addProperty("LOTALIAS",tvTime.text.toString())
                map.addProperty("EQUIPMENTID",tvSite.text.toString())
                viewModel.getLotInfo(map)
                etDurableID.setText("")
            }
        })
        // 设置站点列表的点击监听器，用于弹出站点选择框
        flSiteList.setOnClickListener{
            if (mSitePop == null) {
                mSitePop = PopWindowsUtils.getOneChoosePop(this, flSiteList.width, siteList,
                    ivSiteArrow, object : PopWindowsUtils.PopTextAndPositionCallback {
                        override fun onSuccess(string: String, potion: Int) {
                            tvSite.text = string
                        }
                    }
                )
            }

            if (mSitePop!!.isShowing) {
                mSitePop?.dismiss()
            } else {
                PopWindowsUtils.setArrowShow(ivSiteArrow)
                mSitePop?.showAsDropDown(flSiteList, 0, 0)
            }
        }

        // 观察视图模型的数据变化，更新UI
        viewModel.defUI.callObserve.observe(this){
            val data = it.item as LotInventoryEntity
            tvRecipeName.text = data.RECIPENAME
            tvWaferSize.text = data.WAFERSIZE
            tvRecipeDesc.text = data.RECIPEDESC
            tvOutPart.text = data.OUTPART
            tvOutPqty.text = data.OUTPQTY
        }
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.flTimeList -> {
                mDatePicker!!.show(System.currentTimeMillis())
            }
        }
    }

    private fun initDatePicker() {
        val beginTimestamp = FreeApi_DateUtils.str2Long("1992-03-20", false)
        val endTimestamp = FreeApi_DateUtils.str2Long("2222-02-22", false)

        // 通过时间戳初始化日期，毫秒级别
        mDatePicker = CustomDatePicker(this, { timestamp ->
            beginDate = FreeApi_DateUtils.long2Str(timestamp, false)
            tvTime.text = beginDate
        }, beginTimestamp, endTimestamp)
        // 不允许点击屏幕或物理返回键关闭
        mDatePicker!!.setCancelable(true)
        // 不显示时和分
        mDatePicker!!.setCanShowPreciseTime(false)
        // 不允许循环滚动
        mDatePicker!!.setScrollLoop(false)
        // 不允许滚动动画
        mDatePicker!!.setCanShowAnim(false)
    }

}