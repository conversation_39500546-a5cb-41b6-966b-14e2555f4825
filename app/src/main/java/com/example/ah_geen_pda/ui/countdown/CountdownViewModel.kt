package com.example.ah_geen_pda.ui.countdown

import android.os.Handler
import android.os.Looper
import android.util.Log
import androidx.databinding.ObservableArrayList
import androidx.lifecycle.MutableLiveData
import com.example.ah_geen_pda.R
import com.example.ah_geen_pda.base.BaseViewModel
import com.example.ah_geen_pda.entity.CountdownTimerEntity
import com.example.ah_geen_pda.entity.LotEntity
import com.example.ah_geen_pda.entity.LotMessage
import com.example.ah_geen_pda.entity.ToastMessageBean
import com.example.ah_geen_pda.model.LotInfoRepository
import com.google.gson.Gson
import com.google.gson.JsonObject
import com.google.gson.JsonParser
import me.tatarka.bindingcollectionadapter2.BR
import me.tatarka.bindingcollectionadapter2.ItemBinding

/**
 * 倒计时页面ViewModel
 * 负责管理倒计时逻辑、批次数据和UI状态
 */
class CountdownViewModel : BaseViewModel() {

    companion object {
        private const val TAG = "CountdownViewModel"
    }

    // 数据仓库
    val lotRepository: LotInfoRepository = LotInfoRepository()

    // 批次列表数据
    var lotItems = ObservableArrayList<LotEntity>()

    // 删除批次的点击事件处理
    val onDeleteClick: DeleteLot = object : DeleteLot {
        override fun onDelete(item: LotEntity) {
            Log.d(TAG, "删除批次: ${item.LOT.LOTID}")
            lotItems.remove(item)
            updateLotCount()
        }
    }

    var lotItemBinding = ItemBinding.of<LotEntity>(BR.lotItem, R.layout.item_countdown_lot)
        .bindExtra(BR.onDeleteClick, onDeleteClick)

    // 倒计时相关属性
    private var countdownTimer: CountdownTimerEntity? = null
    private var currentStage = 1 // 当前倒计时阶段：1-第一段，2-第二段
    private var remainingTime = 0 // 剩余时间（秒）
    private var isCountdownRunning = false // 倒计时是否正在运行
    private var handler = Handler(Looper.getMainLooper())
    private var countdownRunnable: Runnable? = null

    // UI状态LiveData
    val countdownText = MutableLiveData<String>()
    val countdownStatus = MutableLiveData<String>()
    val buttonText = MutableLiveData<String>()
    val lotCount = MutableLiveData<String>()
    val showFirstStageCompleteDialog = MutableLiveData<Boolean>()
    val showSecondStageCompleteDialog = MutableLiveData<Boolean>()
    val updateCircularProgress = MutableLiveData<Triple<Int, Int, Int>>() // 当前进度, 最大进度, 剩余时间
    val updateStageInfo = MutableLiveData<Pair<Int, Int>>() // 当前段数, 总段数

    init {
        Log.d(TAG, "CountdownViewModel 初始化")
        initializeUI()
    }

    /**
     * 初始化UI状态
     */
    private fun initializeUI() {
        Log.d(TAG, "初始化UI状态")
        countdownText.value = "0"
        countdownStatus.value = "准备开始"
        buttonText.value = "开始倒计时"
        updateLotCount()
        // 初始化段数显示
        updateStageInfo.value = Pair(1, 1)
        Log.d(TAG, "UI状态初始化完成，包含段数显示")
    }

    /**
     * 更新批次数量显示
     */
    private fun updateLotCount() {
        val count = lotItems.size
        lotCount.value = "($count)"
        Log.d(TAG, "更新批次数量显示: $count")
    }

    /**
     * 根据载具号获取批次信息
     * @param lotid 载具号
     */
    fun getLotInfo(lotid: String) {
        Log.d(TAG, "开始获取载具信息: $lotid")
        
        if (lotid.isBlank()) {
            Log.w(TAG, "载具号为空，跳过获取")
            return
        }

        // 检查是否已存在相同载具
        lotItems.forEach { existingLot ->
            if (existingLot.LOT.DURABLEID == lotid) {
                Log.w(TAG, "载具 $lotid 已存在，显示提示")
                defUI.toastEvent.value = ToastMessageBean("当前载具已添加", false)
                return
            }
        }

        val map = JsonObject().apply {
            addProperty("ACTIONTYPE", "LOTINFOBYDURABLE")
            addProperty("DURABLE", lotid)
        }

        launchOnlyResult({
            try {
                Log.d(TAG, "调用API获取载具信息: $lotid")
                val lotEntity = lotRepository.getLotInfoTo(map)
                
                if (lotEntity.LOT.STATE != "WAIT") {
                    Log.w(TAG, "批次状态不符合要求: ${lotEntity.LOT.STATE}")
                    defUI.toastEvent.value = ToastMessageBean(
                        "当前批次状态为${lotEntity.LOT.STATE},无法进站", false
                    )
                    return@launchOnlyResult
                }

                // 设置批次的起始和结束信息
                if (lotEntity.LOT.COMPONENTLIST.isNotEmpty()) {
                    lotEntity.LOT.start = lotEntity.LOT.COMPONENTLIST[0].ATTRIBUTE1
                    lotEntity.LOT.end = lotEntity.LOT.COMPONENTLIST.last().ATTRIBUTE1
                }

                // 查看批次的RECIPEDESC是否和批次列表中的RECIPEDESC一致
                if(!lotItems.isEmpty()){
                    if(lotEntity.RECIPEDESC != lotItems[0].RECIPEDESC){
                        Log.w(TAG, "批次列表中的RECIPEDESC不一致")
                        defUI.toastEvent.value = ToastMessageBean(
                            "当前批次的RECIPEDESC不一致，请检查", false
                        )
                        return@launchOnlyResult
                    }
                }



                Log.d(TAG, "成功获取批次信息，添加到列表: ${lotEntity.LOT.LOTID}")
                lotItems.add(lotEntity)
                updateLotCount()
                defUI.toastEvent.value = ToastMessageBean("载具添加成功", true)
                
            } catch (e: Exception) {
                Log.e(TAG, "获取载具信息失败", e)
                defUI.toastEvent.value = ToastMessageBean("获取载具信息失败: ${e.message}", false)
            }
        })
    }

    /**
     * 获取倒计时配置并开始倒计时
     */
    fun startCountdown() {
        Log.d(TAG, "开始倒计时流程")
        
        if (lotItems.isEmpty()) {
            Log.w(TAG, "批次列表为空，无法开始倒计时")
            defUI.toastEvent.value = ToastMessageBean("请先添加批次数据", false)
            return
        }

        if (isCountdownRunning || (countdownTimer != null &&currentStage == 1 && countdownTimer!!.hasSecondStage())) {
            Log.d(TAG, "倒计时正在运行，处理继续逻辑")
            handleContinueCountdown()
            return
        }

        // 获取倒计时配置
        getCountdownConfiguration()
    }

    /**
     * 获取倒计时配置
     */
    private fun getCountdownConfiguration() {
        Log.d(TAG, "获取倒计时配置")
        
        val map = JsonObject().apply {
            addProperty("ACTIONTYPE", "GETCOUNTDOWNTIME")
            addProperty("EQUIPMENTID", "DEFAULT_EQUIPMENT") // 可以根据实际需求修改
        }

        launchOnlyResult({
            try {
                Log.d(TAG, "获取倒计时配置")
                // 获取第一个批次中的resp中的时间来进行倒计时
                if (!lotItems.isEmpty()){
                    var recipedesc = lotItems[0].RECIPEDESC;
                    // 使用模拟的数据：15秒到10秒
                    recipedesc = "15→10|xxxxxxxx";
                    // 根据|分割去第一部分
                    recipedesc = recipedesc.split("|")[0]
                    // 根据→去切割第一部分和第二部分分别是多少秒
                    val countdownTime = recipedesc.split("→")

                    val firstStageTime = if (countdownTime.size > 0){
                        countdownTime[0].toInt()
                    }else {
                        0
                    }

                    val secondStageTime = if (countdownTime.size > 1){
                        countdownTime[1].toInt()
                    }else {
                        0
                    }

                    // 配置倒计时配置数据
                    val timerConfig = CountdownTimerEntity(
                        firstStageTime = firstStageTime, // 第一段时间
                        secondStageTime = secondStageTime,  // 第二段时间
                        equipmentId = "DEFAULT_EQUIPMENT",
                        timerType = "PRODUCTION_TIMER",
                        enableVibration = true,
                        enableSound = true,
                        soundResourceId = R.raw.music
                    )
                    Log.d(TAG, "获取到倒计时配置: 第一段${timerConfig.firstStageTime}秒, 第二段${timerConfig.secondStageTime}秒")

                    countdownTimer = timerConfig

                    // 确定总段数并更新段数显示
                    val totalStages = if (timerConfig.hasSecondStage()) 2 else 1
                    updateStageInfo.value = Pair(1, totalStages)
                    Log.d(TAG, "倒计时段数信息: 当前段数=1, 总段数=$totalStages")

                    startFirstStageCountdown()
                }else{
                    defUI.toastEvent.value = ToastMessageBean("请添加批次后重试", false)
                }

            } catch (e: Exception) {
                Log.e(TAG, "获取倒计时配置失败", e)
                defUI.toastEvent.value = ToastMessageBean("获取倒计时配置失败: ${e.message}", false)
            }
        })
    }

    /**
     * 开始第一段倒计时
     */
    private fun startFirstStageCountdown() {
        Log.d(TAG, "开始第一段倒计时")
        
        countdownTimer?.let { timer ->
            currentStage = 1
            remainingTime = timer.firstStageTime
            isCountdownRunning = true

            countdownStatus.value = "第一段倒计时进行中"
            buttonText.value = "倒计时进行中..."

            // 更新段数显示为第一段
            val totalStages = if (timer.hasSecondStage()) 2 else 1
            updateStageInfo.value = Pair(1, totalStages)
            Log.d(TAG, "开始第一段倒计时，段数显示: 1/$totalStages")

            startCountdownTimer(timer.firstStageTime)
        }
    }

    /**
     * 处理继续倒计时（第二段）
     */
    private fun handleContinueCountdown() {
        Log.d(TAG, "处理继续倒计时")
        
        countdownTimer?.let { timer ->
            if (currentStage == 1 && timer.hasSecondStage()) {
                Log.d(TAG, "开始第二段倒计时")
                currentStage = 2
                remainingTime = timer.secondStageTime

                countdownStatus.value = "第二段倒计时进行中"
                buttonText.value = "倒计时进行中..."

                // 更新段数显示为第二段
                updateStageInfo.value = Pair(2, 2)
                Log.d(TAG, "开始第二段倒计时，段数显示: 2/2")

                startCountdownTimer(timer.secondStageTime)
            }
        }
    }

    /**
     * 启动倒计时定时器
     */
    private fun startCountdownTimer(totalSeconds: Int) {
        Log.d(TAG, "启动倒计时定时器，总时长: ${totalSeconds}秒")
        
        // 取消之前的定时器
        countdownRunnable?.let { handler.removeCallbacks(it) }
        
        countdownRunnable = object : Runnable {
            override fun run() {
                Log.v(TAG, "倒计时更新: 第${currentStage}段，剩余${remainingTime}秒")

                if (remainingTime > 0) {
                    // 更新UI
                    countdownText.value = remainingTime.toString()
                    updateCircularProgress.value = Triple(
                        totalSeconds - remainingTime, // 当前进度
                        totalSeconds, // 最大进度
                        remainingTime // 剩余时间
                    )

                    // 在特定时间点输出段数信息
                    when (remainingTime) {
                        totalSeconds -> Log.i(TAG, "【倒计时开始】第${currentStage}段倒计时开始，总时长${totalSeconds}秒")
                        totalSeconds / 2 -> Log.i(TAG, "【倒计时进度】第${currentStage}段倒计时已过半，剩余${remainingTime}秒")
                        10 -> Log.i(TAG, "【倒计时警告】第${currentStage}段倒计时即将结束，剩余${remainingTime}秒")
                        5 -> Log.i(TAG, "【倒计时警告】第${currentStage}段倒计时最后5秒！")
                    }

                    remainingTime--
                    handler.postDelayed(this, 1000) // 1秒后再次执行
                } else {
                    // 倒计时结束
                    Log.d(TAG, "倒计时结束，当前阶段: $currentStage")
                    handleCountdownComplete()
                }
            }
        }
        
        handler.post(countdownRunnable!!)
    }

    /**
     * 处理倒计时完成 - 增强版，包含详细的段数信息输出
     */
    private fun handleCountdownComplete() {
        Log.d(TAG, "处理倒计时完成，当前阶段: $currentStage")

        countdownTimer?.let { timer ->
            when (currentStage) {
                1 -> {
                    Log.i(TAG, "【倒计时完成】第一段倒计时完成！")
                    if (timer.hasSecondStage()) {
                        // 有第二段，显示第一段完成对话框
                        Log.i(TAG, "【倒计时状态】检测到存在第二段倒计时，等待用户确认继续")
                        Log.i(TAG, "【段数信息】当前: 第1段完成，待开始: 第2段")

                        countdownStatus.value = "第一段完成，等待继续"
                        buttonText.value = "继续"
                        showFirstStageCompleteDialog.value = true
                    } else {
                        // 没有第二段，直接执行出站
                        Log.i(TAG, "【倒计时状态】只有一段倒计时，全部完成")
                        Log.i(TAG, "【段数信息】总段数: 1段，已完成: 1段")
                        Log.d(TAG, "只有一段倒计时，直接执行出站")
                        executeTrackOut()
                    }
                }
                2 -> {
                    Log.i(TAG, "【倒计时完成】第二段倒计时完成！")
                    Log.i(TAG, "【倒计时状态】双段倒计时全部完成")
                    Log.i(TAG, "【段数信息】总段数: 2段，已完成: 2段")
                    Log.d(TAG, "第二段倒计时完成")

                    countdownStatus.value = "倒计时完成"
                    showSecondStageCompleteDialog.value = true
                }
            }
        }

        isCountdownRunning = false
        Log.i(TAG, "【倒计时总结】倒计时阶段 $currentStage 完成处理")
    }

    /**
     * 执行批次出站操作
     */
    fun executeTrackOut() {
        Log.d(TAG, "开始执行批次出站操作，批次数量: ${lotItems.size}")
        
        if (lotItems.isEmpty()) {
            Log.w(TAG, "没有批次数据，无法执行出站")
            defUI.toastEvent.value = ToastMessageBean("没有批次数据", false)
            return
        }

        val map = JsonObject().apply {
            addProperty("ACTIONTYPE", "DefaultTrackIn")
            addProperty("EQUIPMENTID", "1-EBB-Z002")
            addProperty("CARRIERID", "")
            addProperty("MATERIAL", "")
        }

        val lotList = mutableListOf<LotMessage>()
        val actionList = mutableListOf<LotMessage>()
        
        lotItems.forEach { lotEntity ->
            val lotMessage = LotMessage(lotEntity.LOT.LOTID)
            val lotActionMessage = LotMessage(
                "", 
                lotEntity.LOT.LOTID, 
                lotEntity.LOT.OBJECTRRN, 
                lotEntity.LOT.MAINQTY
            )
            lotList.add(lotMessage)
            actionList.add(lotActionMessage)
        }

        map.add("LOTLIST", JsonParser().parse(Gson().toJson(lotList)).asJsonArray)

        launchOnlyResult({
            try {
                Log.d(TAG, "调用出站API")
                lotRepository.defaultTrackIn(map)
                Log.d(TAG, "出站操作成功")
                
                defUI.toastEvent.value = ToastMessageBean("出站成功", true)
                
                // 清空批次列表
                lotItems.clear()
                updateLotCount()
                
                // 重置UI状态
                resetCountdownState()
                
                // 返回上一页面
                defUI.startActivity.call()
                
            } catch (e: Exception) {
                Log.e(TAG, "出站操作失败", e)
                defUI.toastEvent.value = ToastMessageBean("出站失败: ${e.message}", false)
            }
        })
    }

    /**
     * 重置倒计时状态
     */
    private fun resetCountdownState() {
        Log.d(TAG, "重置倒计时状态")
        
        // 停止定时器
        countdownRunnable?.let { handler.removeCallbacks(it) }
        countdownRunnable = null
        
        // 重置状态
        isCountdownRunning = false
        currentStage = 1
        remainingTime = 0
        countdownTimer = null
        
        // 重置UI
        initializeUI()
    }

    /**
     * 删除批次接口
     */
    interface DeleteLot {
        fun onDelete(item: LotEntity)
    }

    override fun onCleared() {
        super.onCleared()
        Log.d(TAG, "ViewModel被清理，停止定时器")
        countdownRunnable?.let { handler.removeCallbacks(it) }
    }
}
