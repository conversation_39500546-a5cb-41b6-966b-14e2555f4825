package com.example.ah_geen_pda.ui.chipfrontsectionout

import android.os.Bundle
import com.example.ah_geen_pda.R
import com.example.ah_geen_pda.BR
import com.example.ah_geen_pda.base.BaseActivity
import com.example.ah_geen_pda.databinding.ActivityChipfrontsectionoutBinding
import com.example.ah_geen_pda.entity.ToastMessageBean
import kotlinx.android.synthetic.main.activity_chipfrontsectionout.*
import kotlinx.android.synthetic.main.activity_title.*

class ChipFrontSectionOutAcitvity :BaseActivity<ChipFrontSectionOutViewModel, ActivityChipfrontsectionoutBinding>(){
    override fun layoutId(): Int {
        return R.layout.activity_chipfrontsectionout
    }

    override fun initVariableId(): Int {
        return BR.chipFrontSectionOutViewModel
    }

    override fun initView(savedInstanceState: Bundle?) {
        tvTitle.text = "芯片前段CheckOut"

        initEdit(etLotID,object : EditTextListener{
            override fun onTextChangeListener(s: String) {
                viewModel.getLotInfo(s,false)
            }

        })

        initEdit(etDurable,object : EditTextListener{
            override fun onTextChangeListener(s: String) {
                viewModel.trackOutItems.forEach {
                    if (it.LOT.DURABLEID == s){
                        viewModel.defUI.toastEvent.value = ToastMessageBean("当前载具已添加",false)
                        return
                    }
                }
                viewModel.getLotInfo(s,true)
            }

        })

        btnTrackOut.setOnClickListener{
            if (viewModel.trackOutItems.isEmpty()){
                viewModel.defUI.toastEvent.value = ToastMessageBean("请先添加批次列表",false)
                return@setOnClickListener
            }

            viewModel.trackOut()
        }
    }

    override fun initData() {
        viewModel.defUI.startActivity.observe(this){finish()}
    }
}