package com.example.ah_geen_pda.ui.countdown

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.util.Log
import android.view.View
import androidx.core.content.ContextCompat
import com.example.ah_geen_pda.R

/**
 * 自定义环形进度条控件
 * 用于倒计时页面显示倒计时进度，支持不同时间段显示不同颜色
 * 增强版：支持渐变色、阴影效果、段数显示、动画效果
 */
class CircularProgressView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    companion object {
        private const val TAG = "CircularProgressView"
        // 默认进度条宽度
        private const val DEFAULT_STROKE_WIDTH = 60f
        // 默认进度条半径
        private const val DEFAULT_RADIUS = 150f
    }

    // 画笔对象
    private val backgroundPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val progressPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val textPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val shadowPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val stagePaint = Paint(Paint.ANTI_ALIAS_FLAG) // 段数显示画笔
    private val stageBackgroundPaint = Paint(Paint.ANTI_ALIAS_FLAG) // 段数背景画笔

    // 进度条属性
    private var strokeWidth = DEFAULT_STROKE_WIDTH
    private var radius = DEFAULT_RADIUS
    private var centerX = 0f
    private var centerY = 0f

    // 进度相关属性
    private var maxProgress = 100
    private var currentProgress = 0
    private var animatedProgress = 0f // 动画进度值
    private var progressColor = Color.GREEN
    private var circleBackgroundColor = Color.GRAY

    // 倒计时文本
    private var countdownText = "0"
    private var textSize = 80f
    private var textColor = Color.BLACK

    // 段数显示相关属性
    private var currentStage = 1 // 当前段数
    private var totalStages = 1 // 总段数
    private var stageText = "1/1" // 段数文本
    private var stageTextSize = 24f // 段数文字大小

    // 动画相关
    private var progressAnimator: ValueAnimator? = null

    init {
        Log.d(TAG, "CircularProgressView 初始化 - 增强版")
        initPaints()
    }

    /**
     * 初始化画笔 - 增强版，支持阴影和渐变效果
     */
    private fun initPaints() {
        Log.d(TAG, "初始化画笔设置 - 增强版")

        // 背景圆环画笔
        backgroundPaint.apply {
            color = ContextCompat.getColor(context, R.color.free_ui_line_color)
            style = Paint.Style.STROKE
            strokeWidth = <EMAIL>
            strokeCap = Paint.Cap.ROUND
        }

        // 进度圆环画笔 - 增加阴影效果
        progressPaint.apply {
            color = progressColor
            style = Paint.Style.STROKE
            strokeWidth = <EMAIL>
            strokeCap = Paint.Cap.ROUND
            // 添加阴影效果
            setShadowLayer(8f, 0f, 0f, Color.parseColor("#40000000"))
        }

        // 阴影画笔
        shadowPaint.apply {
            style = Paint.Style.STROKE
            strokeWidth = <EMAIL> + 4f
            color = Color.parseColor("#20000000")
            strokeCap = Paint.Cap.ROUND
        }

        // 倒计时文本画笔
        textPaint.apply {
            color = textColor
            textSize = <EMAIL>
            textAlign = Paint.Align.CENTER
            typeface = Typeface.DEFAULT_BOLD
            // 添加文字阴影
            setShadowLayer(4f, 2f, 2f, Color.parseColor("#40000000"))
        }

        // 段数显示文本画笔
        stagePaint.apply {
            textAlign = Paint.Align.CENTER
            textSize = stageTextSize
            color = ContextCompat.getColor(context, R.color.free_ui_glory_theme_color)
            typeface = Typeface.DEFAULT_BOLD
        }

        // 段数背景画笔
        stageBackgroundPaint.apply {
            style = Paint.Style.FILL
            color = Color.parseColor("#E3F2FD") // 浅蓝色背景
        }

        Log.d(TAG, "画笔初始化完成 - 包含阴影和渐变效果")
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        Log.d(TAG, "控件尺寸变化: w=$w, h=$h")

        centerX = w / 2f
        centerY = h / 2f
        radius = (minOf(w, h) / 2f) - strokeWidth - 20f // 留出边距

        // 创建渐变色效果
        setupGradientColors()

        Log.d(TAG, "计算后的中心点: ($centerX, $centerY), 半径: $radius")
    }

    /**
     * 设置渐变色效果
     */
    private fun setupGradientColors() {
        if (centerX > 0 && centerY > 0 && radius > 0) {
            // 创建径向渐变，从中心向外渐变
            val gradientShader = RadialGradient(
                centerX, centerY, radius,
                intArrayOf(
                    Color.parseColor("#4CAF50"), // 绿色中心
                    Color.parseColor("#2196F3"), // 蓝色边缘
                    Color.parseColor("#FF9800")  // 橙色外围
                ),
                floatArrayOf(0.0f, 0.7f, 1.0f),
                Shader.TileMode.CLAMP
            )

            // 应用渐变到进度条画笔
            progressPaint.shader = gradientShader
            Log.d(TAG, "渐变色效果设置完成")
        }
    }

    override fun onDraw(canvas: Canvas?) {
        super.onDraw(canvas)
        canvas?.let { drawProgress(it) }
    }

    /**
     * 绘制进度条和文本 - 增强版，包含阴影、段数显示等效果
     */
    private fun drawProgress(canvas: Canvas) {
        Log.v(TAG, "绘制进度条: 当前进度=$currentProgress, 最大进度=$maxProgress, 当前段数=$currentStage/$totalStages")

        // 绘制阴影圆环
        canvas.drawCircle(centerX + 2f, centerY + 2f, radius, shadowPaint)

        // 绘制背景圆环
        canvas.drawCircle(centerX, centerY, radius, backgroundPaint)

        // 计算进度角度（使用动画进度值）
        val sweepAngle = if (maxProgress > 0) {
            360f * animatedProgress / maxProgress
        } else {
            0f
        }

        Log.v(TAG, "计算的扫描角度: $sweepAngle (动画进度: $animatedProgress)")

        // 绘制进度圆环（从顶部开始，顺时针方向）
        val rect = RectF(
            centerX - radius,
            centerY - radius,
            centerX + radius,
            centerY + radius
        )

        // 如果有进度，绘制进度弧
        if (sweepAngle > 0) {
            canvas.drawArc(rect, -90f, sweepAngle, false, progressPaint)
        }

        // 绘制倒计时文本
        val textY = centerY + (textPaint.textSize / 2) - textPaint.descent()
        canvas.drawText(countdownText, centerX, textY, textPaint)

        // 绘制段数显示
        drawStageIndicator(canvas)

        Log.v(TAG, "绘制完成: 文本=$countdownText, 段数=$stageText")
    }

    /**
     * 绘制段数指示器
     */
    private fun drawStageIndicator(canvas: Canvas) {
        if (totalStages > 1) {
            Log.d(TAG, "绘制段数指示器: $stageText")

            // 计算段数显示位置（在圆环下方）
            val stageY = centerY + radius + strokeWidth + 40f

            // 绘制段数背景
            val textWidth = stagePaint.measureText(stageText)
            val backgroundRect = RectF(
                centerX - textWidth / 2 - 16f,
                stageY - stageTextSize / 2 - 8f,
                centerX + textWidth / 2 + 16f,
                stageY + stageTextSize / 2 + 8f
            )
            canvas.drawRoundRect(backgroundRect, 12f, 12f, stageBackgroundPaint)

            // 绘制段数文本
            canvas.drawText(stageText, centerX, stageY + stageTextSize / 4, stagePaint)

            Log.v(TAG, "段数指示器绘制完成: $stageText 在位置 ($centerX, $stageY)")
        }
    }

    /**
     * 设置最大进度值
     */
    fun setMaxProgress(max: Int) {
        Log.d(TAG, "设置最大进度: $max")
        this.maxProgress = max
        invalidate()
    }

    /**
     * 设置当前进度值 - 带动画效果
     */
    fun setProgress(progress: Int) {
        Log.v(TAG, "设置当前进度: $progress")
        val targetProgress = progress.coerceIn(0, maxProgress)
        this.currentProgress = targetProgress

        // 启动进度动画
        startProgressAnimation(targetProgress.toFloat())
    }

    /**
     * 启动进度动画
     */
    private fun startProgressAnimation(targetProgress: Float) {
        Log.d(TAG, "启动进度动画: 从 $animatedProgress 到 $targetProgress")

        // 取消之前的动画
        progressAnimator?.cancel()

        progressAnimator = ValueAnimator.ofFloat(animatedProgress, targetProgress).apply {
            duration = 300 // 300ms动画时长
            addUpdateListener { animator ->
                animatedProgress = animator.animatedValue as Float
                invalidate()
            }
            start()
        }
    }

    /**
     * 设置倒计时文本
     */
    fun setCountdownText(text: String) {
        Log.d(TAG, "设置倒计时文本: $text")
        this.countdownText = text
        invalidate()
    }

    /**
     * 根据剩余时间设置进度条颜色 - 增强版，支持更丰富的颜色变化
     * @param remainingTime 剩余时间（秒）
     * @param totalTime 总时间（秒）
     */
    fun updateProgressColor(remainingTime: Int, totalTime: Int) {
        val progressRatio = if (totalTime > 0) remainingTime.toFloat() / totalTime else 0f

        progressColor = when {
            progressRatio > 0.6f -> {
                Log.d(TAG, "进度条颜色设置为绿色 (剩余时间比例: $progressRatio)")
                Color.parseColor("#4CAF50") // 更鲜艳的绿色
            }
            progressRatio > 0.3f -> {
                Log.d(TAG, "进度条颜色设置为橙色 (剩余时间比例: $progressRatio)")
                Color.parseColor("#FF9800") // 更鲜艳的橙色
            }
            else -> {
                Log.d(TAG, "进度条颜色设置为红色 (剩余时间比例: $progressRatio)")
                Color.parseColor("#F44336") // 更鲜艳的红色
            }
        }

        // 重新设置渐变色
        setupGradientColors()
        invalidate()
    }

    /**
     * 设置倒计时段数信息
     * @param current 当前段数
     * @param total 总段数
     */
    fun setStageInfo(current: Int, total: Int) {
        Log.d(TAG, "设置段数信息: 当前段数=$current, 总段数=$total")
        this.currentStage = current
        this.totalStages = total
        this.stageText = "$current/$total"

        // 根据段数更新颜色主题
        updateStageTheme()
        invalidate()
    }

    /**
     * 根据当前段数更新主题颜色
     */
    private fun updateStageTheme() {
        val stageColor = when (currentStage) {
            1 -> Color.parseColor("#2196F3") // 第一段：蓝色
            2 -> Color.parseColor("#FF9800") // 第二段：橙色
            else -> Color.parseColor("#4CAF50") // 其他：绿色
        }

        stagePaint.color = stageColor
        Log.d(TAG, "根据段数 $currentStage 更新主题颜色")
    }

    /**
     * 设置进度条宽度
     */
    fun setStrokeWidth(width: Float) {
        Log.d(TAG, "设置进度条宽度: $width")
        this.strokeWidth = width
        backgroundPaint.strokeWidth = width
        progressPaint.strokeWidth = width
        invalidate()
    }

    /**
     * 设置文本大小
     */
    fun setTextSize(size: Float) {
        Log.d(TAG, "设置文本大小: $size")
        this.textSize = size
        textPaint.textSize = size
        invalidate()
    }
}
