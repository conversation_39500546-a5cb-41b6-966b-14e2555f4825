package com.example.ah_geen_pda.ui.countdown

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.util.Log
import android.view.View
import androidx.core.content.ContextCompat
import com.example.ah_geen_pda.R
import kotlin.math.*
import kotlin.random.Random

/**
 * 时间流逝感倒计时控件
 * 重新设计的倒计时视图，让用户能够感受到时间慢慢消失的感觉
 * 特效包括：沙漏效果、粒子消散、波纹扩散、时间条融化等
 */
class CircularProgressView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    companion object {
        private const val TAG = "TimeFlowProgressView"
        // 粒子相关常量
        private const val MAX_PARTICLES = 50
        private const val PARTICLE_LIFE_TIME = 2000L // 粒子生命周期2秒
        // 波纹相关常量
        private const val MAX_RIPPLES = 3
        private const val RIPPLE_DURATION = 1500L // 波纹持续时间1.5秒
        // 时间条相关常量
        private const val TIME_BAR_HEIGHT = 40f
        private const val MELTING_SPEED = 2f // 融化速度
    }

    // 画笔对象 - 重新设计的画笔集合
    private val backgroundPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val timeBarPaint = Paint(Paint.ANTI_ALIAS_FLAG) // 时间条画笔
    private val meltingPaint = Paint(Paint.ANTI_ALIAS_FLAG) // 融化效果画笔
    private val particlePaint = Paint(Paint.ANTI_ALIAS_FLAG) // 粒子画笔
    private val ripplePaint = Paint(Paint.ANTI_ALIAS_FLAG) // 波纹画笔
    private val textPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val shadowPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val stagePaint = Paint(Paint.ANTI_ALIAS_FLAG) // 段数显示画笔

    // 控件基本属性
    private var centerX = 0f
    private var centerY = 0f
    private var viewWidth = 0f
    private var viewHeight = 0f

    // 时间相关属性
    private var maxProgress = 100
    private var currentProgress = 0
    private var animatedProgress = 0f // 动画进度值
    private var remainingTimeRatio = 1.0f // 剩余时间比例

    // 倒计时文本
    private var countdownText = "0"
    private var textSize = 80f
    private var textColor = Color.BLACK

    // 段数显示相关属性
    private var currentStage = 1 // 当前段数
    private var totalStages = 1 // 总段数
    private var stageText = "1/1" // 段数文本
    private var stageTextSize = 24f // 段数文字大小

    // 动画相关
    private var progressAnimator: ValueAnimator? = null
    private var particleAnimator: ValueAnimator? = null
    private var rippleAnimator: ValueAnimator? = null

    // 粒子系统 - 用于表现时间消散效果
    private val particles = mutableListOf<TimeParticle>()
    private var lastParticleTime = 0L

    // 波纹系统 - 用于表现时间流逝的波动
    private val ripples = mutableListOf<TimeRipple>()
    private var lastRippleTime = 0L

    // 融化效果相关
    private val meltingDrops = mutableListOf<MeltingDrop>()
    private var meltingProgress = 0f

    init {
        Log.d(TAG, "时间流逝感倒计时控件初始化开始")
        initPaints()
        startContinuousAnimations()
        Log.d(TAG, "时间流逝感倒计时控件初始化完成")
    }

    /**
     * 时间粒子数据类 - 表示消散的时间粒子
     */
    private data class TimeParticle(
        var x: Float,
        var y: Float,
        var vx: Float, // x方向速度
        var vy: Float, // y方向速度
        var alpha: Float, // 透明度
        var size: Float, // 大小
        val birthTime: Long, // 出生时间
        val color: Int // 颜色
    ) {
        fun update(deltaTime: Long) {
            // 更新位置
            x += vx * deltaTime / 16f // 假设60fps
            y += vy * deltaTime / 16f

            // 更新透明度（随时间衰减）
            val age = System.currentTimeMillis() - birthTime
            alpha = (1f - age.toFloat() / PARTICLE_LIFE_TIME).coerceAtLeast(0f)

            // 更新大小（逐渐变小）
            size *= 0.995f
        }

        fun isAlive(): Boolean {
            return System.currentTimeMillis() - birthTime < PARTICLE_LIFE_TIME && alpha > 0.01f
        }
    }

    /**
     * 时间波纹数据类 - 表示时间流逝的波动
     */
    private data class TimeRipple(
        val centerX: Float,
        val centerY: Float,
        var radius: Float,
        var alpha: Float,
        val birthTime: Long,
        val maxRadius: Float
    ) {
        fun update() {
            val age = System.currentTimeMillis() - birthTime
            val progress = age.toFloat() / RIPPLE_DURATION

            // 波纹半径扩大
            radius = maxRadius * progress

            // 透明度衰减
            alpha = (1f - progress).coerceAtLeast(0f)
        }

        fun isAlive(): Boolean {
            return System.currentTimeMillis() - birthTime < RIPPLE_DURATION
        }
    }

    /**
     * 融化水滴数据类 - 表示时间条融化效果
     */
    private data class MeltingDrop(
        var x: Float,
        var y: Float,
        var vy: Float, // 下落速度
        var alpha: Float,
        var size: Float,
        val color: Int
    ) {
        fun update() {
            y += vy
            vy += 0.5f // 重力加速度
            alpha *= 0.98f // 逐渐透明
            size *= 0.99f // 逐渐变小
        }

        fun isAlive(): Boolean {
            return alpha > 0.01f && size > 1f
        }
    }

    /**
     * 初始化画笔 - 时间流逝感设计
     */
    private fun initPaints() {
        Log.d(TAG, "初始化时间流逝感画笔设置")

        // 背景画笔 - 深色背景营造时间流逝氛围
        backgroundPaint.apply {
            color = Color.parseColor("#1A1A1A") // 深灰色背景
            style = Paint.Style.FILL
        }

        // 时间条画笔 - 主要的时间显示条
        timeBarPaint.apply {
            style = Paint.Style.FILL
            strokeCap = Paint.Cap.ROUND
            // 初始为蓝色，会根据时间变化
            color = Color.parseColor("#2196F3")
            setShadowLayer(6f, 0f, 2f, Color.parseColor("#80000000"))
        }

        // 融化效果画笔 - 时间条融化时使用
        meltingPaint.apply {
            style = Paint.Style.FILL
            strokeCap = Paint.Cap.ROUND
        }

        // 粒子画笔 - 时间粒子消散效果
        particlePaint.apply {
            style = Paint.Style.FILL
            strokeCap = Paint.Cap.ROUND
        }

        // 波纹画笔 - 时间波动效果
        ripplePaint.apply {
            style = Paint.Style.STROKE
            strokeWidth = 3f
            strokeCap = Paint.Cap.ROUND
        }

        // 倒计时文本画笔 - 更突出的文字效果
        textPaint.apply {
            color = Color.WHITE
            textSize = <EMAIL>
            textAlign = Paint.Align.CENTER
            typeface = Typeface.create(Typeface.DEFAULT, Typeface.BOLD)
            // 添加发光效果
            setShadowLayer(8f, 0f, 0f, Color.parseColor("#80FFFFFF"))
        }

        // 阴影画笔 - 增强立体感
        shadowPaint.apply {
            color = Color.parseColor("#40000000")
            style = Paint.Style.FILL
        }

        // 段数显示文本画笔
        stagePaint.apply {
            textAlign = Paint.Align.CENTER
            textSize = stageTextSize
            color = Color.parseColor("#FFD700") // 金色
            typeface = Typeface.DEFAULT_BOLD
            setShadowLayer(4f, 1f, 1f, Color.parseColor("#80000000"))
        }

        Log.d(TAG, "时间流逝感画笔初始化完成")
    }

    /**
     * 启动连续动画效果 - 粒子和波纹的持续动画
     */
    private fun startContinuousAnimations() {
        Log.d(TAG, "启动连续动画效果")

        // 粒子动画 - 持续产生和更新粒子
        particleAnimator = ValueAnimator.ofFloat(0f, 1f).apply {
            duration = Long.MAX_VALUE // 无限循环
            repeatCount = ValueAnimator.INFINITE
            addUpdateListener {
                updateParticles()
                generateNewParticles()
                invalidate()
            }
            start()
        }

        // 波纹动画 - 定期产生波纹效果
        rippleAnimator = ValueAnimator.ofFloat(0f, 1f).apply {
            duration = Long.MAX_VALUE // 无限循环
            repeatCount = ValueAnimator.INFINITE
            addUpdateListener {
                updateRipples()
                generateNewRipples()
                invalidate()
            }
            start()
        }

        Log.d(TAG, "连续动画效果启动完成")
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        Log.d(TAG, "控件尺寸变化: w=$w, h=$h")

        viewWidth = w.toFloat()
        viewHeight = h.toFloat()
        centerX = w / 2f
        centerY = h / 2f

        // 设置时间条的渐变效果
        setupTimeBarGradient()

        Log.d(TAG, "计算后的中心点: ($centerX, $centerY), 视图尺寸: ($viewWidth, $viewHeight)")
    }

    /**
     * 设置时间条渐变效果 - 根据剩余时间动态变化
     */
    private fun setupTimeBarGradient() {
        if (viewWidth > 0 && viewHeight > 0) {
            Log.d(TAG, "设置时间条渐变效果，剩余时间比例: $remainingTimeRatio")

            // 根据剩余时间比例选择颜色
            val colors = when {
                remainingTimeRatio > 0.6f -> {
                    Log.d(TAG, "时间充足 - 使用蓝绿渐变")
                    intArrayOf(
                        Color.parseColor("#00BCD4"), // 青色
                        Color.parseColor("#2196F3"), // 蓝色
                        Color.parseColor("#3F51B5")  // 深蓝
                    )
                }
                remainingTimeRatio > 0.3f -> {
                    Log.d(TAG, "时间紧张 - 使用橙黄渐变")
                    intArrayOf(
                        Color.parseColor("#FFC107"), // 黄色
                        Color.parseColor("#FF9800"), // 橙色
                        Color.parseColor("#FF5722")  // 深橙
                    )
                }
                else -> {
                    Log.d(TAG, "时间紧急 - 使用红色渐变")
                    intArrayOf(
                        Color.parseColor("#FF5722"), // 深橙
                        Color.parseColor("#F44336"), // 红色
                        Color.parseColor("#D32F2F")  // 深红
                    )
                }
            }

            // 创建线性渐变
            val gradient = LinearGradient(
                0f, centerY - TIME_BAR_HEIGHT,
                viewWidth, centerY + TIME_BAR_HEIGHT,
                colors,
                floatArrayOf(0f, 0.5f, 1f),
                Shader.TileMode.CLAMP
            )

            timeBarPaint.shader = gradient
            Log.d(TAG, "时间条渐变效果设置完成")
        }
    }

    override fun onDraw(canvas: Canvas?) {
        super.onDraw(canvas)
        canvas?.let { drawTimeFlowEffect(it) }
    }

    /**
     * 绘制时间流逝效果 - 全新设计的时间感知视觉效果
     */
    private fun drawTimeFlowEffect(canvas: Canvas) {
        Log.v(TAG, "绘制时间流逝效果: 当前进度=$currentProgress, 最大进度=$maxProgress, 剩余时间比例=$remainingTimeRatio")

        // 1. 绘制背景
        canvas.drawRect(0f, 0f, viewWidth, viewHeight, backgroundPaint)

        // 2. 绘制背景波纹效果
        drawBackgroundRipples(canvas)

        // 3. 绘制主要时间条
        drawMainTimeBar(canvas)

        // 4. 绘制融化效果
        drawMeltingEffect(canvas)

        // 5. 绘制时间粒子
        drawTimeParticles(canvas)

        // 6. 绘制倒计时文本
        drawCountdownText(canvas)

        // 7. 绘制段数指示器
        drawStageIndicator(canvas)

        Log.v(TAG, "时间流逝效果绘制完成")
    }

    /**
     * 绘制背景波纹效果 - 营造时间流动的氛围
     */
    private fun drawBackgroundRipples(canvas: Canvas) {
        Log.v(TAG, "绘制背景波纹，当前波纹数量: ${ripples.size}")

        ripples.forEach { ripple ->
            if (ripple.isAlive()) {
                ripplePaint.apply {
                    color = Color.parseColor("#40FFFFFF")
                    alpha = (ripple.alpha * 255).toInt()
                }
                canvas.drawCircle(ripple.centerX, ripple.centerY, ripple.radius, ripplePaint)
            }
        }
    }

    /**
     * 绘制主要时间条 - 水平时间条，随时间减少
     */
    private fun drawMainTimeBar(canvas: Canvas) {
        Log.v(TAG, "绘制主要时间条，剩余时间比例: $remainingTimeRatio")

        // 计算时间条的宽度（基于剩余时间）
        val barWidth = viewWidth * 0.8f * remainingTimeRatio
        val barHeight = TIME_BAR_HEIGHT
        val barLeft = (viewWidth - viewWidth * 0.8f) / 2f
        val barTop = centerY - barHeight / 2f

        // 绘制时间条阴影
        shadowPaint.alpha = 100
        canvas.drawRoundRect(
            barLeft + 3f, barTop + 3f,
            barLeft + barWidth + 3f, barTop + barHeight + 3f,
            barHeight / 2f, barHeight / 2f,
            shadowPaint
        )

        // 绘制主时间条
        canvas.drawRoundRect(
            barLeft, barTop,
            barLeft + barWidth, barTop + barHeight,
            barHeight / 2f, barHeight / 2f,
            timeBarPaint
        )

        // 在时间条末端添加融化效果
        if (remainingTimeRatio < 0.8f) {
            generateMeltingDrops(barLeft + barWidth, barTop + barHeight)
        }

        Log.v(TAG, "主要时间条绘制完成，宽度: $barWidth")
    }

    /**
     * 绘制段数指示器
     */
    private fun drawStageIndicator(canvas: Canvas) {
        if (totalStages > 1) {
            Log.d(TAG, "绘制段数指示器: $stageText")

            // 计算段数显示位置（在圆环下方）
            val stageY = centerY + radius + strokeWidth + 40f

            // 绘制段数背景
            val textWidth = stagePaint.measureText(stageText)
            val backgroundRect = RectF(
                centerX - textWidth / 2 - 16f,
                stageY - stageTextSize / 2 - 8f,
                centerX + textWidth / 2 + 16f,
                stageY + stageTextSize / 2 + 8f
            )
            canvas.drawRoundRect(backgroundRect, 12f, 12f, stageBackgroundPaint)

            // 绘制段数文本
            canvas.drawText(stageText, centerX, stageY + stageTextSize / 4, stagePaint)

            Log.v(TAG, "段数指示器绘制完成: $stageText 在位置 ($centerX, $stageY)")
        }
    }

    /**
     * 设置最大进度值
     */
    fun setMaxProgress(max: Int) {
        Log.d(TAG, "设置最大进度: $max")
        this.maxProgress = max
        invalidate()
    }

    /**
     * 设置当前进度值 - 带动画效果
     */
    fun setProgress(progress: Int) {
        Log.v(TAG, "设置当前进度: $progress")
        val targetProgress = progress.coerceIn(0, maxProgress)
        this.currentProgress = targetProgress

        // 启动进度动画
        startProgressAnimation(targetProgress.toFloat())
    }

    /**
     * 启动进度动画
     */
    private fun startProgressAnimation(targetProgress: Float) {
        Log.d(TAG, "启动进度动画: 从 $animatedProgress 到 $targetProgress")

        // 取消之前的动画
        progressAnimator?.cancel()

        progressAnimator = ValueAnimator.ofFloat(animatedProgress, targetProgress).apply {
            duration = 300 // 300ms动画时长
            addUpdateListener { animator ->
                animatedProgress = animator.animatedValue as Float
                invalidate()
            }
            start()
        }
    }

    /**
     * 设置倒计时文本
     */
    fun setCountdownText(text: String) {
        Log.d(TAG, "设置倒计时文本: $text")
        this.countdownText = text
        invalidate()
    }

    /**
     * 根据剩余时间设置进度条颜色 - 增强版，支持更丰富的颜色变化
     * @param remainingTime 剩余时间（秒）
     * @param totalTime 总时间（秒）
     */
    fun updateProgressColor(remainingTime: Int, totalTime: Int) {
        val progressRatio = if (totalTime > 0) remainingTime.toFloat() / totalTime else 0f

        progressColor = when {
            progressRatio > 0.6f -> {
                Log.d(TAG, "进度条颜色设置为绿色 (剩余时间比例: $progressRatio)")
                Color.parseColor("#4CAF50") // 更鲜艳的绿色
            }
            progressRatio > 0.3f -> {
                Log.d(TAG, "进度条颜色设置为橙色 (剩余时间比例: $progressRatio)")
                Color.parseColor("#FF9800") // 更鲜艳的橙色
            }
            else -> {
                Log.d(TAG, "进度条颜色设置为红色 (剩余时间比例: $progressRatio)")
                Color.parseColor("#F44336") // 更鲜艳的红色
            }
        }

        // 重新设置渐变色
        setupGradientColors()
        invalidate()
    }

    /**
     * 设置倒计时段数信息
     * @param current 当前段数
     * @param total 总段数
     */
    fun setStageInfo(current: Int, total: Int) {
        Log.d(TAG, "设置段数信息: 当前段数=$current, 总段数=$total")
        this.currentStage = current
        this.totalStages = total
        this.stageText = "$current/$total"

        // 根据段数更新颜色主题
        updateStageTheme()
        invalidate()
    }

    /**
     * 根据当前段数更新主题颜色
     */
    private fun updateStageTheme() {
        val stageColor = when (currentStage) {
            1 -> Color.parseColor("#2196F3") // 第一段：蓝色
            2 -> Color.parseColor("#FF9800") // 第二段：橙色
            else -> Color.parseColor("#4CAF50") // 其他：绿色
        }

        stagePaint.color = stageColor
        Log.d(TAG, "根据段数 $currentStage 更新主题颜色")
    }

    /**
     * 设置进度条宽度
     */
    fun setStrokeWidth(width: Float) {
        Log.d(TAG, "设置进度条宽度: $width")
        this.strokeWidth = width
        backgroundPaint.strokeWidth = width
        progressPaint.strokeWidth = width
        invalidate()
    }

    /**
     * 设置文本大小
     */
    fun setTextSize(size: Float) {
        Log.d(TAG, "设置文本大小: $size")
        this.textSize = size
        textPaint.textSize = size
        invalidate()
    }
}
