package com.example.ah_geen_pda.ui.urgentsystem

import android.util.Log
import androidx.databinding.ObservableArrayList
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.example.ah_geen_pda.R
import com.example.ah_geen_pda.base.BaseViewModel
import com.example.ah_geen_pda.entity.UrgentFilmInformation
import com.example.ah_geen_pda.model.LotInfoRepository
import me.tatarka.bindingcollectionadapter2.BR
import me.tatarka.bindingcollectionadapter2.ItemBinding

class UrgentSystemViewModel : BaseViewModel() {

    var dataItems = ObservableArrayList<UrgentFilmInformation>()
    var durableDataBinding =
        ItemBinding.of<UrgentFilmInformation>(BR.dataItem, R.layout.item_urgent_system_list)
    private val _dataItemsCount = MutableLiveData<Int>()
    val dataItemsCount: LiveData<Int> get() = _dataItemsCount

    val loginRepository: LotInfoRepository = LotInfoRepository()

    fun fetchUrgentFilmInfo(site: String) {
        // 验证输入参数
        if (site.isNullOrEmpty()) {
            Log.w("UrgentSystemViewModel", "站点名称为空，无法获取加急系统数据")
            return
        }

        // 安全日志记录
        Log.d("UrgentSystemViewModel", "开始获取加急系统数据")

        // 启动一个协程来执行异步任务，并处理结果
        launchOnlyResult({
            try {
                // 清空数据项和组件项列表以准备接收新的数据
                dataItems.clear()
                // 通过调用登录仓库的getUrgentFilmInformation方法获取车位信息
                val newDataItems = loginRepository.getUrgentFilmInformation(site)
                if (newDataItems != null) {
                    dataItems.addAll(newDataItems)
                    _dataItemsCount.postValue(dataItems.size)
                } else {
                    Log.w("UrgentSystemViewModel", "获取的数据为空")
                    _dataItemsCount.postValue(0)
                }
            } catch (e: Exception) {
                Log.e("UrgentSystemViewModel", "获取加急系统数据时发生错误", e)
                _dataItemsCount.postValue(0)
            }
        })
    }

    /**
     * 获取加急系统数据，并使用回调函数返回数量 (主要于后台的数据获取)
     * @param site 站点名称
     * @param callback 回调函数，接收一个Int参数，表示获取的数据数量
     * @return 无返回值
     */
    fun fetchUrgentFilmInfoWithCallback(site: String, callback: (Int) -> Unit) {

        if (site.isNullOrEmpty()) {
            Log.w("UrgentSystemViewModel", "站点名称为空，无法获取加急系统数据")
            return
        }

        Log.d("UrgentSystemViewModel", "开始获取加急系统数据")

        launchOnlyResult({
            try {
                var dataItems_Size_Off = 0
                val newDataItems = loginRepository.getUrgentFilmInformation(site)
                newDataItems?.forEachIndexed { index, item ->
                    if (item.Detain != "On") {
                        dataItems_Size_Off++
                    }
                }
                if (newDataItems != null) {
                    dataItems.clear()
                    dataItems.addAll(newDataItems)
                    _dataItemsCount.postValue(dataItems.size)
                    callback(dataItems_Size_Off)  // 回调返回数量
                } else {
                    Log.w("UrgentSystemViewModel", "获取的数据为空")
                    _dataItemsCount.postValue(0)
                    callback(0)
                }
            } catch (e: Exception) {
                Log.e("UrgentSystemViewModel", "获取加急系统数据时发生错误", e)
                _dataItemsCount.postValue(0)
                callback(0)
            }
        })
    }
}
