package com.example.ah_geen_pda.ui.urgentsystem

import android.app.*
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.util.Log
import androidx.core.app.NotificationCompat
import com.example.ah_geen_pda.R
import com.example.ah_geen_pda.utils.VibrationAndMusicUtils

class UrgentTimerService : Service() {
    private val handler = Handler(Looper.getMainLooper())
    private var timeInterval = 3 * 60 * 1000L // 30分钟提醒一次
    private var currentSite: String = "请选择站点"
    private val viewModel: UrgentSystemViewModel by lazy { UrgentSystemViewModel() }
    private var isTimerRunning = false

    private val runnable = object : Runnable {
        override fun run() {
            Log.i("UrgentTimerService", "定时任务执行: ${timeInterval}")
            if (currentSite != "请选择站点") {
                viewModel.fetchUrgentFilmInfoWithCallback(currentSite) { count ->
                    if (count > 0) {
                        showUrgentNotification(count)
                        VibrationAndMusicUtils.startVibration(this@UrgentTimerService)
                        VibrationAndMusicUtils.startMusic(this@UrgentTimerService, R.raw.urgent_film_prompt_sound2)
                    }
                }
            }
            handler.postDelayed(this, timeInterval)
        }
    }

    override fun onCreate() {
        super.onCreate()
        startForeground(NOTIFICATION_ID, createNotification())
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        intent?.let {
            val newSite = it.getStringExtra("site") ?: "请选择站点"
            if (currentSite != newSite) {
                currentSite = newSite
                // 如果站点改变，重新启动定时任务
                restartTimer()
            }
        }
        
        if (!isTimerRunning) {
            startTimer()
        }
        
        return START_STICKY
    }

    private fun startTimer() {
        if (!isTimerRunning) {
            handler.post(runnable)
            isTimerRunning = true
        }
    }

    private fun stopTimer() {
        handler.removeCallbacks(runnable)
        isTimerRunning = false
    }

    private fun restartTimer() {
        stopTimer()
        startTimer()
    }

    override fun onDestroy() {
        super.onDestroy()
        stopTimer()
    }

    override fun onBind(intent: Intent?): IBinder? = null

    /**
     * 创建一个通知
     *
     * 此函数负责生成一个Notification对象，用于在Oreo及以上版本的Android系统中展示后台服务的运行状态
     * 它首先检查设备的API级别，如果是Oreo或更高版本，则创建并注册一个通知渠道
     * 之后，使用NotificationCompat.Builder构建一个通知对象，设置其各项属性，如标题、文本和图标等
     *
     * @return 返回构建好的Notification对象
     */
    private fun createNotification(): Notification {
        // 检查设备是否运行Oreo或更高版本的Android系统
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            // 创建一个通知渠道，用于Oreo及以上版本的通知兼容性
            val channel = NotificationChannel(
                CHANNEL_ID,
                "Urgent Timer Channel",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Channel for urgent timer service"
            }
            // 获取系统的NotificationManager服务，用于管理通知渠道
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            // 在系统中创建通知渠道，确保通知可以被正确显示
            notificationManager.createNotificationChannel(channel)
        }

        // 使用NotificationCompat.Builder构建通知，设置必要的属性
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("加急片监控服务")
            .setContentText("正在后台运行")
            .setSmallIcon(R.drawable.log_tb)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .build()
    }

    /**
     * 显示紧急通知的方法
     * 当有紧急情况需要用户立即关注时调用此方法
     *
     * @param count 需要处理的紧急事项数量
     */
    private fun showUrgentNotification(count: Int) {
        // 获取通知管理器实例
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

        // 从Android Oreo开始，通知必须关联到一个通知渠道
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            // 创建一个高优先级的通知渠道
            val channel = NotificationChannel(
                URGENT_CHANNEL_ID,
                "Urgent Alert Channel",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Channel for urgent alerts"
                enableVibration(true)
                enableLights(true)
            }
            // 发布通知渠道
            notificationManager.createNotificationChannel(channel)
        }

        // 创建一个Intent，用于在通知点击时启动活动 - 修复：不清除任务栈
        val intent = Intent(this, UrgentSystemActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_SINGLE_TOP or Intent.FLAG_ACTIVITY_CLEAR_TOP
            putExtra("site", currentSite)
        }
        // 创建一个PendingIntent，封装启动活动的Intent
        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            intent,
            PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
        )

        // 构建通知对象
        val notification = NotificationCompat.Builder(this, URGENT_CHANNEL_ID)
            .setContentTitle("发现新的加急片!")
            .setContentText("您有 $count 盒数据待处理，请尽快安排作业")
            .setSmallIcon(R.drawable.log_tb)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setAutoCancel(true)
            .setContentIntent(pendingIntent)
            .build()

        // 发送通知
        notificationManager.notify(URGENT_NOTIFICATION_ID, notification)
    }

    companion object {
        private const val CHANNEL_ID = "urgent_timer_channel"
        private const val NOTIFICATION_ID = 2
        private const val URGENT_CHANNEL_ID = "urgent_alert_channel"
        private const val URGENT_NOTIFICATION_ID = 3

        /**
         * 启动服务的函数
         *
         * 此函数根据当前的Android版本选择合适的方式启动服务
         * 在Android Oreo及以上版本中，使用startForegroundService以适应后台执行限制
         * 在旧版本中，继续使用startService方法
         *
         * @param context 上下文，用于启动服务
         * @param site 一个字符串参数，表示站点或位置信息，会被作为额外数据传递给服务
         */
        fun startService(context: Context, site: String) {
            // 创建一个Intent对象，用于启动UrgentTimerService
            val intent = Intent(context, UrgentTimerService::class.java).apply {
                // 将站点信息作为额外数据添加到Intent中
                putExtra("site", site)
            }
            // 检查当前Android版本是否为Oreo或更高版本
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                // 对于Oreo及以上版本，使用startForegroundService启动服务
                context.startForegroundService(intent)
            } else {
                // 对于旧版本，使用startService启动服务
                context.startService(intent)
            }
        }

        fun stopService(context: Context) {
            context.stopService(Intent(context, UrgentTimerService::class.java))
        }
    }
} 