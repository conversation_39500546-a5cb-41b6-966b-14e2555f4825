package com.example.ah_geen_pda.ui.chipfrontsectionout

import androidx.databinding.ObservableArrayList
import com.example.ah_geen_pda.R
import com.example.ah_geen_pda.base.BaseViewModel
import com.example.ah_geen_pda.entity.LOTBean
import com.example.ah_geen_pda.entity.LotEntity
import com.example.ah_geen_pda.entity.LotMessage
import com.example.ah_geen_pda.entity.ToastMessageBean
import com.example.ah_geen_pda.model.LotInfoRepository
import com.google.gson.Gson
import com.google.gson.JsonObject
import com.google.gson.JsonParser
import me.tatarka.bindingcollectionadapter2.BR
import me.tatarka.bindingcollectionadapter2.ItemBinding

class ChipFrontSectionOutViewModel:BaseViewModel() {
    var trackOutItems = ObservableArrayList<LotEntity>()

    val onClick : DeleteLot = object : DeleteLot {
        override fun onDelete(item: LotEntity) {
            trackOutItems.remove(item)
        }
    }
    var trackOutItemBinding =
        ItemBinding.of<LotEntity>(BR.trackOutItem, R.layout.item_track_out)
            .bindExtra(BR.onDeleteClick,onClick)

    val lotRepository: LotInfoRepository = LotInfoRepository()

    fun getLotInfo(lotid: String,b:Boolean) {
        val map = JsonObject()
        if (b){
            map.addProperty("ACTIONTYPE","LOTINFOBYDURABLE")
            map.addProperty("DURABLE",lotid)
        }else{
            map.addProperty("ACTIONTYPE","LOTINFOBYID")
            map.addProperty("LOTID",lotid)
        }
        launchOnlyResult({
            val lotEntity = lotRepository.getLotInfo(map)
            if(lotEntity.LOT.STATE == "WAIT"){
                if (b){
                    if (lotEntity.STEPATTRIBUTE != "PlatingCarrierID"){
                        defUI.toastEvent.value =
                            ToastMessageBean("当前批次状态为" + lotEntity.LOT.STATE + ",无法出站", false)
                        return@launchOnlyResult
                    }
                }else {
                    defUI.toastEvent.value =
                        ToastMessageBean("当前批次状态为" + lotEntity.LOT.STATE + ",无法出站", false)
                    return@launchOnlyResult
                }
            }
            lotEntity.LOT.start = lotEntity.LOT.COMPONENTLIST[0].ATTRIBUTE1
            lotEntity.LOT.end = lotEntity.LOT.COMPONENTLIST[lotEntity.LOT.COMPONENTLIST.size-1].ATTRIBUTE1
            trackOutItems.add(lotEntity)
        })
    }

    fun trackOut() {
        val map = JsonObject()
        map.addProperty("ACTIONTYPE","DefaultTrackOut")
        map.addProperty("EQUIPMENTID",trackOutItems[0].LOT.EQUIPMENTID)
        map.addProperty("CARRIERACTION","")
        map.addProperty("DURABLE","")
        map.addProperty("ALLREWORKFLAG","N")
        var list = mutableListOf<LotMessage>()
        var actionList = mutableListOf<LotMessage>()
        trackOutItems.forEach {
            val lotMessage = LotMessage(it.LOT.LOTID)
            val lotActionMessage = LotMessage("",it.LOT.LOTID,it.LOT.OBJECTRRN,it.LOT.MAINQTY)
            list.add(lotMessage)
            actionList.add(lotActionMessage)
        }
        map.add("LOTLIST", JsonParser().parse(Gson().toJson(list)).asJsonArray)
//        map.add("LOTACTIONLIST", JsonParser().parse(Gson().toJson(actionList)).asJsonArray)
        launchOnlyResult({
            lotRepository.defaultTrackOut(map)
            defUI.toastEvent.value = ToastMessageBean("出站成功", true)
            defUI.startActivity.call()
        })

    }

    interface DeleteLot{
        fun onDelete(item : LotEntity)
    }


}