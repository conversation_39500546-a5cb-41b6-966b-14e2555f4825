package com.example.ah_geen_pda.setting

import android.os.Bundle
import android.view.View
import com.example.ah_geen_pda.R
import com.example.ah_geen_pda.BR
import com.example.ah_geen_pda.base.BaseActivity
import com.example.ah_geen_pda.databinding.ActivitySettingBinding
import com.example.ah_geen_pda.utils.PopWindowsUtils
import kotlinx.android.synthetic.main.activity_setting.*
import java.util.*


class SettingActivity : BaseActivity<SettingViewModel, ActivitySettingBinding>(),
    View.OnClickListener {
    override fun layoutId(): Int {
        return R.layout.activity_setting
    }

    override fun initVariableId(): Int {
        return BR.settingViewModel
    }

    override fun initView(savedInstanceState: Bundle?) {
        setTitle("设置")
        viewModel.initVersion(this)
        rlIP.setOnClickListener(this)
    }

    override fun initData() {

    }

    override fun onClick(v: View?) {
        when(v?.id){
            R.id.rlIP ->{
                viewModel.showScanDialog(this)
            }
        }
    }
}