package com.example.ah_geen_pda.setting

import android.app.Activity
import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.MutableLiveData
import com.example.ah_geen_pda.base.BaseViewModel
import com.example.ah_geen_pda.base.Constant
import com.example.ah_geen_pda.base.MyApp
import com.example.ah_geen_pda.entity.ToastMessageBean
import com.pda.platform.ui.ui_pdaplatform.dialog.FreeUI_GeneralFragmentDialog
import com.pda.platform.ui.ui_pdaplatform.utils_public.FreeApi_DialogUtils
import com.pda.platform.ui.ui_pdaplatform.utils_public.FreeApi_SharePreferencesUtils

class SettingViewModel : BaseViewModel() {

    private var mSeverDialog: FreeUI_GeneralFragmentDialog? = null
    var constant = MutableLiveData<Constant.Companion>()

    fun initVersion(context: Activity){
        constant.value = Constant.Companion
        val manager: PackageManager = context.packageManager
        var info: PackageInfo? = null
        try {
            info = manager.getPackageInfo(context.packageName, 0)
        } catch (e: PackageManager.NameNotFoundException) {
            e.printStackTrace()
        }
        val version = info!!.versionName
        constant.value?.VRESION = version

    }


    /**
     * type 0代表REQUEST_URL_DEBUG  1 代表FILE_URL
     */
    fun showScanDialog(context: AppCompatActivity){
        mSeverDialog = FreeApi_DialogUtils.getScanDialog(context, "请输入服务器地址",
            Constant.REQUEST_URL_DEBUG) {

            constant.value!!.REQUEST_URL_DEBUG = it

            defUI.toastEvent.value = ToastMessageBean("保存成功", true)
            FreeApi_SharePreferencesUtils.setSharePre("FILE", "REQUEST_URL_DEBUG", it, MyApp.app)
            mSeverDialog?.dismiss()
        }
        mSeverDialog?.show(context.supportFragmentManager, "WEB_URL_DIALOG")
    }

}