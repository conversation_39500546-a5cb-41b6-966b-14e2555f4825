[{"picName": "datacheck", "showPlugin": "header", "text": "资料核对", "skipClass": "com.example.ah_geen_pda.ui.datacheck.DataCheckActivity"}, {"picName": "inventory", "showPlugin": "header", "text": "盘点", "skipClass": "com.example.ah_geen_pda.ui.inventory.InventoryActivity"}, {"picName": "in", "showPlugin": "header", "text": "芯片前段CheckIn", "skipClass": "com.example.ah_geen_pda.ui.chipfrontsectionin.ChipFrontSectionInAcitvity"}, {"picName": "out", "showPlugin": "header", "text": "芯片前段CheckOut", "skipClass": "com.example.ah_geen_pda.ui.chipfrontsectionout.ChipFrontSectionOutAcitvity"}, {"picName": "jaji", "showPlugin": "header", "text": "加急片资料", "skipClass": "com.example.ah_geen_pda.ui.urgentsystem.UrgentSystemActivity"}, {"picName": "timer", "showPlugin": "header", "text": "计时器", "skipClass": "com.example.ah_geen_pda.ui.countdown.CountdownActivity"}]